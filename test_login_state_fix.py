#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试登录状态修复
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_login_data_copy():
    """测试登录数据复制逻辑"""
    try:
        from core.video_material_manager import VideoMaterialManager
        from core.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建视频素材管理器
        manager = VideoMaterialManager(config_manager)
        
        print("=== 登录数据复制测试 ===")
        print(f"源目录: {manager.chrome_debug_dir}")
        
        # 检查源目录中的关键文件
        print("\n=== 检查源目录中的关键文件 ===")
        critical_files = [
            "Default/Network/Cookies",  # 新版Chrome的Cookie位置
            "Default/Cookies",          # 旧版Chrome的Cookie位置
            "Default/Login Data",
            "Default/Web Data",
            "Default/Preferences"
        ]
        
        for file_path in critical_files:
            full_path = os.path.join(manager.chrome_debug_dir, file_path)
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                print(f"✅ {file_path}: {file_size} 字节")
            else:
                print(f"❌ {file_path}: 不存在")
        
        # 测试数据复制
        print("\n=== 测试数据复制 ===")
        import tempfile
        import uuid
        test_target = os.path.join(tempfile.gettempdir(), f"test-chrome-{uuid.uuid4().hex[:8]}")
        
        try:
            success = manager.copy_essential_chrome_data(manager.chrome_debug_dir, test_target)
            print(f"复制结果: {'成功' if success else '失败'}")
            
            if os.path.exists(test_target):
                print(f"目标目录已创建: {test_target}")
                
                # 检查复制的文件
                print("\n=== 检查复制的文件 ===")
                for file_path in critical_files:
                    target_file = os.path.join(test_target, file_path)
                    if os.path.exists(target_file):
                        file_size = os.path.getsize(target_file)
                        print(f"✅ {file_path}: {file_size} 字节")
                    else:
                        print(f"❌ {file_path}: 未复制")
                
                # 清理测试目录
                import shutil
                shutil.rmtree(test_target)
                print(f"\n🧹 已清理测试目录")
            
        except Exception as e:
            print(f"复制测试失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_login_data_copy()
    if success:
        print("\n✅ 测试通过")
    else:
        print("\n❌ 测试失败")
        sys.exit(1)
