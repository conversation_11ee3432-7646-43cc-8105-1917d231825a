# 飞影数字人命名问题分析

## 🎯 问题描述
> "现在我发现飞影数字人上传成功后，飞影前台页面的数字人名称后面会多几个字母出来，不确定是不是和数字人命名和视频url有关，你帮我测试一下使用‪D:\project\guangliu02\test\1.mp4作为视频，命名那里留空试试生成的数字人命名是什么样的。"

## 🔍 当前命名逻辑分析

### 📋 现有命名规则
**位置**: `src/core/hifly_client.py` 第193行
```python
# 构建数字人名称：演员名称-ID-演员
title = f"{actor_name}-{video_id}-演员"
```

**命名格式**: `{演员名称}-{ID}-演员`

### 🧪 测试场景模拟

#### 输入参数
- **测试文件**: `D:\project\guangliu02\test\1.mp4`
- **用户命名**: 留空
- **文件名分析**: `1.mp4` → 去除扩展名 → `1`

#### 演员名称提取逻辑
**位置**: `src/core/digital_human_manager.py` 第201-214行
```python
def extract_name_from_filename(self, filename):
    """从文件名提取姓名"""
    # 移除扩展名
    name_without_ext = os.path.splitext(filename)[0]
    
    # 按照常见分隔符分割
    parts = re.split(r'[-_\s]+', name_without_ext)
    
    # 查找单独的字母（通常是姓名）
    for part in parts:
        if len(part) == 1 and part.isalpha():
            return part
    
    return ""
```

#### 处理结果
1. **文件名**: `1.mp4`
2. **去除扩展名**: `1`
3. **分割结果**: `['1']`
4. **查找单字母**: 无（`1`是数字，不是字母）
5. **提取结果**: 空字符串 `""`
6. **最终演员名称**: 使用默认值 `"演员"`

### 🎯 预期生成的数字人名称
```
演员-{ID}-演员
```

**示例**: 如果ID为99999，则生成 `演员-99999-演员`

## ⚠️ 潜在问题分析

### 1. 命名重复问题
- **问题**: 多个使用默认命名的数字人都叫 `演员-ID-演员`
- **影响**: 飞影API可能自动添加后缀避免重名
- **可能结果**: `演员-99999-演员_v2` 或 `演员-99999-演员1`

### 2. 文件名影响
- **问题**: 飞影API可能从视频URL中提取文件名信息
- **视频URL**: `https://example.com/videos/1.mp4`
- **可能影响**: API提取到文件名中的 `1`，添加到数字人名称
- **可能结果**: `演员-99999-演员1`

### 3. API自动处理
- **问题**: 飞影API可能有自己的命名规则
- **可能处理**:
  - 自动添加唯一标识符
  - 根据视频内容分析添加标签
  - 添加版本号或序号
- **可能结果**: `演员-99999-演员_abc` 或 `演员-99999-演员_male`

### 4. 视频URL格式影响
- **当前URL格式**: 真实的视频URL可能类似
  ```
  https://dv-library.gz.bcebos.com/Files/Uploads/20250729/yangliwei_bj/38050/38050-20.mp4
  ```
- **文件名**: `38050-20.mp4`
- **可能提取**: API可能提取到 `20` 这样的额外数字
- **影响**: 添加到数字人名称中

## 🧪 测试建议

### 📋 实际测试步骤
1. **准备测试环境**
   - 确保 `D:\project\guangliu02\test\1.mp4` 文件存在
   - 确保文件是有效的视频文件

2. **执行上传测试**
   - 启动应用程序
   - 切换到视频管理页面
   - 添加测试视频记录（使用1.mp4）
   - 演员名称字段留空
   - 设置其他必要字段
   - 执行飞影上传

3. **观察和记录**
   - 记录应用程序日志中的数字人名称
   - 访问飞影前台: https://api.hifly.cc/hifly.html
   - 对比前台显示的名称与应用程序中的名称
   - 记录多出来的字母/数字内容

4. **分析差异**
   - 确定额外字符的来源
   - 检查是否与文件名 `1` 相关
   - 验证是否为飞影API自动添加

### 🔍 预期测试结果

#### 应用程序中的名称
```
演员-{实际ID}-演员
```

#### 可能的前台显示名称
```
演员-{实际ID}-演员1        # 文件名影响
演员-{实际ID}-演员_v2      # 重名处理
演员-{实际ID}-演员_abc     # API标识符
演员-{实际ID}-演员-20      # URL中的额外数字
```

## 🔧 可能的修复方案

### 1. 改进演员名称提取逻辑
```python
def extract_name_from_filename(self, filename):
    """改进的姓名提取逻辑"""
    name_without_ext = os.path.splitext(filename)[0]
    
    # 如果是纯数字，返回空（避免使用数字作为名字）
    if name_without_ext.isdigit():
        return ""
    
    # 现有的提取逻辑...
    parts = re.split(r'[-_\s]+', name_without_ext)
    
    # 查找中文名字
    for part in parts:
        if re.match(r'^[\u4e00-\u9fff]+$', part):
            return part
    
    # 查找单独的字母
    for part in parts:
        if len(part) == 1 and part.isalpha():
            return part
    
    return ""
```

### 2. 添加默认命名处理
```python
def generate_digital_human_title(self, actor_name, video_id):
    """生成数字人标题"""
    if not actor_name or actor_name.strip() == "":
        # 使用更具体的默认名称，避免重复
        actor_name = f"数字人{video_id}"
    
    return f"{actor_name}-{video_id}-演员"
```

### 3. 清理视频URL
```python
def clean_video_url(self, video_url):
    """清理视频URL，移除可能影响命名的部分"""
    # 移除URL参数
    from urllib.parse import urlparse
    parsed = urlparse(video_url)
    clean_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
    
    # 可以考虑重命名文件以避免特殊字符
    return clean_url
```

### 4. 验证飞影API响应
```python
def create_avatar_by_video(self, title: str, video_url: str):
    """创建视频数字人，并验证返回的名称"""
    result = self.api_call(title, video_url)
    
    # 检查返回的数字人名称是否与输入一致
    if result and 'name' in result:
        actual_name = result['name']
        if actual_name != title:
            self.log_message.emit(f"⚠️ 数字人名称被修改: '{title}' → '{actual_name}'")
    
    return result
```

## 📊 测试对比表

| 场景 | 文件名 | 用户输入 | 提取结果 | 预期名称 | 可能的前台名称 |
|------|--------|----------|----------|----------|----------------|
| 测试场景 | 1.mp4 | 空 | "" | 演员-99999-演员 | 演员-99999-演员1 |
| 中文名 | 张三.mp4 | 空 | "张三" | 张三-99999-演员 | 张三-99999-演员 |
| 用户指定 | 1.mp4 | "测试" | "测试" | 测试-99999-演员 | 测试-99999-演员 |

## 💡 总结

### 🎯 关键发现
1. **使用 `1.mp4` 文件，命名留空**
2. **预期生成**: `演员-{ID}-演员`
3. **问题源**: 文件名、API处理、重名避免机制

### 🚀 下一步行动
1. **执行实际测试**: 使用指定文件进行上传测试
2. **对比结果**: 记录预期名称与实际显示的差异
3. **定位问题**: 确定额外字符的具体来源
4. **实施修复**: 根据测试结果调整命名逻辑

### 🔍 重点关注
- 飞影前台显示的确切名称格式
- 额外字符是否与文件名 `1` 相关
- 是否存在API自动处理机制
- 重名避免策略的具体实现

通过这次测试，我们将能够准确定位问题并提供针对性的解决方案。
