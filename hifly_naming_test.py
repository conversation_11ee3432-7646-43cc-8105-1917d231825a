#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
飞影数字人命名测试
专门测试使用1.mp4文件，命名留空时的实际效果
"""

import os
import sys
import time
import requests
import json
from datetime import datetime

class HiflyNamingTest:
    """飞影命名测试器"""
    
    def __init__(self, token: str):
        self.token = token
        self.base_url = "https://hfw-api.hifly.cc"
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
    def test_connection(self):
        """测试API连接"""
        print("🔗 测试飞影API连接...")
        
        try:
            url = f"{self.base_url}/api/v2/hifly/account/credit"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API连接成功")
                credit = data.get('credit', 'N/A')
                print(f"💰 账户积分: {credit}")
                return True
            else:
                print(f"❌ API连接失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 连接测试失败: {str(e)}")
            return False
    
    def create_avatar_with_url(self, title: str, video_url: str):
        """使用视频URL创建数字人"""
        print(f"\n🎭 创建数字人测试...")
        print(f"📝 输入名称: '{title}'")
        print(f"🔗 视频URL: {video_url}")
        
        try:
            url = f"{self.base_url}/api/v2/hifly/avatar/create_by_video"
            
            payload = {
                "title": title,
                "video_url": video_url
            }
            
            response = requests.post(url, json=payload, headers=self.headers, timeout=30)
            
            print(f"📊 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 数字人创建请求成功")
                print(f"📋 API响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                task_id = data.get('task_id') or data.get('id')
                return task_id, data
            else:
                print(f"❌ 数字人创建失败: {response.status_code}")
                print(f"📄 错误信息: {response.text}")
                return None, None
                
        except Exception as e:
            print(f"❌ 创建异常: {str(e)}")
            return None, None
    
    def check_task_status(self, task_id: str):
        """查询任务状态"""
        try:
            url = f"{self.base_url}/api/v2/hifly/avatar/task"
            params = {"task_id": task_id}
            
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return data
            else:
                print(f"❌ 状态查询失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 状态查询异常: {str(e)}")
            return None
    
    def wait_and_check_result(self, task_id: str, max_wait: int = 180):
        """等待并检查最终结果"""
        print(f"\n⏳ 等待数字人创建完成...")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            status_data = self.check_task_status(task_id)
            
            if status_data:
                status = status_data.get('status')
                print(f"📊 当前状态: {status}")
                
                if status in ['completed', 'success']:
                    print(f"🎉 创建完成！")
                    return status_data
                elif status in ['failed', 'error']:
                    print(f"💔 创建失败")
                    return status_data
            
            time.sleep(15)  # 每15秒检查一次
        
        print(f"⏰ 等待超时")
        return self.check_task_status(task_id)

def get_token():
    """获取API Token"""
    # 1. 尝试从环境变量获取
    token = os.environ.get('HIFLY_API_TOKEN')
    if token:
        return token
    
    # 2. 尝试从配置文件获取
    try:
        with open('config/settings.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
            token = config.get('hifly_token') or config.get('hifly_api_token')
            if token and token != "test_token_here":
                return token
    except:
        pass
    
    # 3. 提示用户输入
    print("🔑 需要飞影API Token进行测试")
    print("💡 获取方式: https://api.hifly.cc/hifly.html")
    token = input("请输入Token: ").strip()
    return token if token else None

def get_naming_choice():
    """获取命名选择"""
    print("\n🎯 选择命名方式:")
    print("1. 使用默认命名 (留空，测试默认行为)")
    print("2. 自定义输入名称")

    while True:
        choice = input("请选择 (1 或 2): ").strip()

        if choice == "1":
            print("✅ 选择: 使用默认命名 (留空)")
            return ""
        elif choice == "2":
            custom_name = input("请输入自定义名称: ").strip()
            print(f"✅ 选择: 自定义名称 '{custom_name}'")
            return custom_name
        else:
            print("❌ 请输入 1 或 2")

def simulate_naming_logic(user_input=""):
    """模拟当前的命名逻辑"""
    print(f"\n🎯 模拟命名逻辑...")

    # 测试参数
    video_file = "1.mp4"
    video_id = "99999"  # 模拟ID

    print(f"📁 视频文件: {video_file}")
    print(f"📝 用户输入: '{user_input}' {'(留空)' if not user_input else ''}")

    # 演员名称提取逻辑
    if user_input.strip():
        actor_name = user_input.strip()
        print(f"👤 使用用户输入的名称: '{actor_name}'")
    else:
        # 从文件名提取
        name_without_ext = os.path.splitext(video_file)[0]  # "1"

        # 查找单独的字母
        import re
        parts = re.split(r'[-_\s]+', name_without_ext)

        extracted_name = ""
        for part in parts:
            if len(part) == 1 and part.isalpha():
                extracted_name = part
                break

        if extracted_name:
            actor_name = extracted_name
            print(f"👤 从文件名提取: '{actor_name}'")
        else:
            actor_name = "演员"  # 默认值
            print(f"👤 无法提取，使用默认值: '{actor_name}'")

    # 生成标题
    title = f"{actor_name}-{video_id}-演员"

    print(f"🎭 最终生成标题: '{title}'")

    return title

def main():
    """主测试函数"""
    print("🧪 飞影数字人命名测试")
    print("=" * 50)

    # 获取命名选择
    user_input = get_naming_choice()

    # 获取Token
    token = get_token()
    if not token:
        print("❌ 未提供Token，无法进行测试")
        return

    # 创建测试器
    tester = HiflyNamingTest(token)

    # 测试连接
    if not tester.test_connection():
        print("❌ API连接失败")
        return

    # 模拟命名逻辑
    title = simulate_naming_logic(user_input)
    
    # 使用一个公开的测试视频URL（或者您可以先上传1.mp4获取URL）
    # 这里我使用一个示例URL，实际测试时需要替换为真实的视频URL
    test_video_url = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
    
    print(f"\n📹 使用测试视频URL: {test_video_url}")
    print(f"💡 注意: 实际测试时应该使用1.mp4上传后的URL")
    
    # 创建数字人
    task_id, create_result = tester.create_avatar_with_url(title, test_video_url)
    
    if not task_id:
        print("❌ 数字人创建失败")
        return
    
    print(f"🆔 任务ID: {task_id}")
    
    # 等待完成并检查结果
    final_result = tester.wait_and_check_result(task_id)
    
    # 分析结果
    print(f"\n" + "=" * 50)
    print(f"📊 测试结果分析")
    print(f"=" * 50)
    
    print(f"🎯 输入信息:")
    print(f"  原始文件: 1.mp4")
    print(f"  用户命名: 留空")
    print(f"  生成标题: '{title}'")
    
    if final_result:
        actual_name = final_result.get('name') or final_result.get('title')
        avatar_id = final_result.get('avatar_id') or final_result.get('avatar')
        
        print(f"\n🎭 API返回结果:")
        print(f"  数字人ID: {avatar_id}")
        print(f"  返回名称: '{actual_name}'")
        
        if actual_name:
            if actual_name == title:
                print(f"✅ 名称完全一致")
            else:
                print(f"⚠️ 名称存在差异:")
                print(f"    预期: '{title}'")
                print(f"    实际: '{actual_name}'")
                
                # 分析差异
                if actual_name.startswith(title):
                    extra = actual_name[len(title):]
                    print(f"    额外字符: '{extra}'")
                elif title in actual_name:
                    print(f"    包含预期名称，但有修改")
                else:
                    print(f"    完全不同的命名")
        
        print(f"\n🔗 查看结果:")
        print(f"  飞影前台: https://api.hifly.cc/hifly.html")
        print(f"  查找ID: {avatar_id}")
        print(f"  对比前台显示的名称与API返回的名称")
    
    print(f"\n💡 测试完成！")
    print(f"请访问飞影前台查看实际显示的数字人名称")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n⏹️ 测试被中断")
    except Exception as e:
        print(f"\n💥 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
