#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频素材管理器
负责从网站下载素材表格，处理数据并更新本地文件
"""

import os
import sys
import pandas as pd
import asyncio
import subprocess
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from PySide6.QtCore import QObject, Signal, QThread
import tempfile
import shutil

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️ Playwright未安装，视频素材管理功能将受限")

# 尝试导入Chrome MCP工具作为备选
try:
    # 这里可以导入Chrome MCP相关工具
    # 例如：from chrome_mcp import ChromeManager
    CHROME_MCP_AVAILABLE = False  # 暂时设为False，等待具体实现
except ImportError:
    CHROME_MCP_AVAILABLE = False


class VideoMaterialManager(QObject):
    """视频素材管理器"""
    
    # 信号定义
    log_message = Signal(str)
    progress_updated = Signal(int, int)  # 当前进度, 总进度
    update_completed = Signal(bool, str)  # 是否成功, 消息
    
    def __init__(self, config_manager=None):
        super().__init__()
        self.config_manager = config_manager
        
        # 文件路径 - 使用绝对路径确保正确性
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.data_dir = os.path.join(current_dir, "data")
        self.temp_dir = os.path.join(self.data_dir, "temp")
        self.avatar_list_path = os.path.join(self.data_dir, "avatar_list.xlsx")

        # 调试信息
        self.log_message.emit(f"📁 数据目录: {self.data_dir}")
        self.log_message.emit(f"📄 素材文件路径: {self.avatar_list_path}")
        self.log_message.emit(f"📋 文件是否存在: {os.path.exists(self.avatar_list_path)}")
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # Chrome调试配置目录 - 使用默认Chrome用户数据目录
        self.chrome_debug_dir = self.get_default_chrome_user_data_dir()

        # 调试信息
        self.log_message.emit(f"🔧 Chrome用户数据目录: {self.chrome_debug_dir}")
        self.log_message.emit(f"🍪 将使用现有的登录状态和Cookie")
        
        # 网站配置
        self.website_url = "https://zxsc.baidu-int.com/ccbms/Home/WorkVideo/WorkVideoManage/Report"

        # 浏览器调试端口配置
        self.debug_port = 9222
        self.original_debug_port = 9222

    def find_available_debug_port(self) -> int:
        """查找可用的调试端口"""
        import socket

        # 尝试的端口范围
        ports_to_try = [9222, 9223, 9224, 9225, 9226]

        for port in ports_to_try:
            try:
                # 检查端口是否被占用
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('127.0.0.1', port))
                    if result != 0:  # 端口未被占用
                        self.log_message.emit(f"🔍 找到可用调试端口: {port}")
                        return port
                    else:
                        self.log_message.emit(f"🔍 端口 {port} 已被占用")
            except Exception as e:
                self.log_message.emit(f"🔍 检查端口 {port} 时出错: {str(e)}")

        # 如果所有预设端口都被占用，返回默认端口
        self.log_message.emit("⚠️ 所有预设端口都被占用，使用默认端口 9222")
        return 9222

    def get_default_chrome_user_data_dir(self) -> str:
        """获取默认Chrome用户数据目录"""
        import platform
        import os

        system = platform.system()

        if system == "Windows":
            # Windows Chrome用户数据目录
            chrome_dirs = [
                os.path.expanduser("~/AppData/Local/Google/Chrome/User Data"),
                os.path.expanduser("~/AppData/Local/Chromium/User Data"),
                "C:/Users/<USER>/AppData/Local/Google/Chrome/User Data".format(os.getenv('USERNAME', '')),
            ]
        elif system == "Darwin":  # macOS
            chrome_dirs = [
                os.path.expanduser("~/Library/Application Support/Google/Chrome"),
                os.path.expanduser("~/Library/Application Support/Chromium"),
            ]
        else:  # Linux
            chrome_dirs = [
                os.path.expanduser("~/.config/google-chrome"),
                os.path.expanduser("~/.config/chromium"),
            ]

        # 查找存在的Chrome用户数据目录
        for chrome_dir in chrome_dirs:
            if os.path.exists(chrome_dir):
                self.log_message.emit(f"🔍 找到Chrome用户数据目录: {chrome_dir}")
                return chrome_dir

        # 如果没有找到默认目录，创建一个备用目录
        fallback_dir = os.path.join(os.path.expanduser("~/Documents"), "GuangliuAssistant", "chrome-profile")
        os.makedirs(fallback_dir, exist_ok=True)
        self.log_message.emit(f"⚠️ 未找到默认Chrome目录，使用备用目录: {fallback_dir}")
        return fallback_dir

    def clear_chrome_debug_data(self) -> bool:
        """清理Chrome登录相关数据（用于重置登录状态）"""
        try:
            if not os.path.exists(self.chrome_debug_dir):
                self.log_message.emit(f"📁 Chrome用户数据目录不存在: {self.chrome_debug_dir}")
                return False

            # 先尝试关闭可能正在运行的Chrome进程
            self.kill_debug_chrome_processes()

            # 等待进程完全关闭
            import time
            time.sleep(2)

            # 清理登录相关的文件和目录
            login_related_items = [
                "Default/Cookies",
                "Default/Cookies-journal",
                "Default/Login Data",
                "Default/Login Data-journal",
                "Default/Web Data",
                "Default/Web Data-journal",
                "Default/Local Storage",
                "Default/Session Storage",
                "Default/Network Action Predictor",
                "Default/Network Action Predictor-journal",
                "Default/Top Sites",
                "Default/Top Sites-journal",
                "Default/Visited Links",
                "Default/History",
                "Default/History-journal",
            ]

            cleared_count = 0
            for item in login_related_items:
                item_path = os.path.join(self.chrome_debug_dir, item)
                try:
                    if os.path.exists(item_path):
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                        else:
                            import shutil
                            shutil.rmtree(item_path)
                        cleared_count += 1
                        self.log_message.emit(f"🧹 已清理: {item}")
                except Exception as e:
                    self.log_message.emit(f"⚠️ 清理 {item} 时出错: {str(e)}")

            if cleared_count > 0:
                self.log_message.emit(f"✅ 已清理 {cleared_count} 个登录相关项目，登录状态已重置")
                self.log_message.emit(f"💡 下次启动Chrome时需要重新登录")
            else:
                self.log_message.emit(f"📋 未找到需要清理的登录数据")

            return True

        except Exception as e:
            self.log_message.emit(f"❌ 清理登录数据失败: {str(e)}")
            return False

    def check_debug_browser(self) -> bool:
        """检查调试端口浏览器是否可用"""
        try:
            import requests
            # 使用IPv4地址避免IPv6连接问题，增加连接超时时间
            response = requests.get(f"http://127.0.0.1:{self.debug_port}/json", timeout=5)
            if response.status_code == 200:
                # 检查是否有可用的页面
                data = response.json()
                if data and len(data) > 0:
                    self.log_message.emit(f"🔍 检测到 {len(data)} 个浏览器页面")
                    return True
            return False
        except Exception as e:
            # 不显示每次检查的异常，避免日志过多
            if "10061" not in str(e):  # 只显示非连接拒绝的异常
                self.log_message.emit(f"🔍 调试端口检查异常: {str(e)}")
            return False

    def check_existing_chrome_processes(self) -> bool:
        """检查是否有现有的Chrome进程在使用默认用户数据目录"""
        try:
            if PSUTIL_AVAILABLE:
                import psutil
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                            cmdline = proc.info['cmdline']
                            if cmdline and any('--user-data-dir' in arg for arg in cmdline):
                                # 检查是否使用了相同的用户数据目录
                                for arg in cmdline:
                                    if '--user-data-dir' in arg and self.chrome_debug_dir in arg:
                                        self.log_message.emit(f"🔍 发现现有Chrome进程使用相同用户数据目录: PID {proc.info['pid']}")
                                        return True
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            return False
        except Exception as e:
            self.log_message.emit(f"🔍 检查现有Chrome进程时出错: {str(e)}")
            return False

    def copy_essential_chrome_data(self, source_dir: str, target_dir: str) -> bool:
        """复制关键的Chrome用户数据到独立目录"""
        try:
            import shutil
            import os

            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)

            # 需要复制的关键文件和目录
            essential_items = [
                "Default/Cookies",
                "Default/Login Data",
                "Default/Web Data",
                "Default/Preferences",
                "Default/Local State",
                "Default/Secure Preferences",
                "Local State"
            ]

            copied_count = 0
            for item in essential_items:
                source_path = os.path.join(source_dir, item)
                target_path = os.path.join(target_dir, item)

                try:
                    if os.path.exists(source_path):
                        # 确保目标目录存在
                        os.makedirs(os.path.dirname(target_path), exist_ok=True)

                        if os.path.isfile(source_path):
                            shutil.copy2(source_path, target_path)
                        else:
                            shutil.copytree(source_path, target_path, dirs_exist_ok=True)
                        copied_count += 1
                        self.log_message.emit(f"📋 已复制: {item}")
                except Exception as e:
                    self.log_message.emit(f"⚠️ 复制 {item} 时出错: {str(e)}")

            if copied_count > 0:
                self.log_message.emit(f"✅ 已复制 {copied_count} 个关键数据文件")
                return True
            else:
                self.log_message.emit("❌ 未能复制任何关键数据文件")
                return False

        except Exception as e:
            self.log_message.emit(f"❌ 复制Chrome数据失败: {str(e)}")
            return False

    def start_debug_browser(self) -> bool:
        """启动带调试端口的浏览器"""
        try:
            import subprocess
            import platform
            import time

            # 检查是否有现有Chrome进程使用相同的用户数据目录
            if self.check_existing_chrome_processes():
                self.log_message.emit("🔍 检测到现有Chrome进程使用相同用户数据目录")
                self.log_message.emit("🔄 将尝试使用独立的调试配置，不影响现有窗口")

                # 查找可用的调试端口
                available_port = self.find_available_debug_port()
                if available_port != self.debug_port:
                    self.debug_port = available_port
                    self.log_message.emit(f"🔄 使用调试端口: {self.debug_port}")

                # 创建独立的调试用户数据目录
                import tempfile
                import uuid
                debug_suffix = uuid.uuid4().hex[:8]
                temp_debug_dir = os.path.join(tempfile.gettempdir(), f"guangliu-chrome-debug-{debug_suffix}")

                # 复制关键的用户数据到临时目录
                if self.copy_essential_chrome_data(self.chrome_debug_dir, temp_debug_dir):
                    self.chrome_debug_dir = temp_debug_dir
                    self.log_message.emit(f"✅ 已创建独立调试目录: {self.chrome_debug_dir}")
                else:
                    self.log_message.emit("⚠️ 创建独立调试目录失败，将使用原目录")

            if platform.system() == "Windows":
                # Windows Chrome路径
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME'))
                ]

                chrome_path = None
                for path in chrome_paths:
                    if os.path.exists(path):
                        chrome_path = path
                        break

                if chrome_path:
                    # 只有在使用独立调试目录时才关闭调试进程
                    if "guangliu-chrome-debug" in self.chrome_debug_dir:
                        self.log_message.emit("🔄 清理可能存在的调试进程...")
                        self.kill_debug_chrome_processes()
                        time.sleep(2)
                    else:
                        self.log_message.emit("🔄 保留现有Chrome进程，使用共享模式")
                    
                    # 确保Chrome调试目录存在且可写
                    try:
                        os.makedirs(self.chrome_debug_dir, exist_ok=True)
                        # 测试目录是否可写
                        test_file = os.path.join(self.chrome_debug_dir, "test_write.tmp")
                        with open(test_file, 'w') as f:
                            f.write("test")
                        os.remove(test_file)
                        self.log_message.emit(f"✅ Chrome调试目录已创建: {self.chrome_debug_dir}")
                    except Exception as e:
                        self.log_message.emit(f"⚠️ Chrome调试目录创建失败: {str(e)}")
                        # 使用系统临时目录作为备选
                        import uuid
                        self.chrome_debug_dir = os.path.join(tempfile.gettempdir(), f"chrome-debug-{uuid.uuid4().hex[:8]}")
                        os.makedirs(self.chrome_debug_dir, exist_ok=True)
                        self.log_message.emit(f"🔄 使用备选目录: {self.chrome_debug_dir}")
                    
                    # 启动参数 - 使用现有Chrome用户数据，保留登录状态
                    startup_args = [
                        chrome_path,
                        f"--remote-debugging-port={self.debug_port}",
                        f"--remote-debugging-address=0.0.0.0",  # 监听所有接口
                        f"--user-data-dir={self.chrome_debug_dir}",  # 使用现有用户数据目录
                        "--no-first-run",
                        "--no-default-browser-check",
                        "--disable-web-security",  # 允许跨域访问
                        "--disable-features=VizDisplayCompositor",
                        "--remote-allow-origins=*",  # 允许跨域访问
                        "--disable-dev-shm-usage",  # 避免共享内存问题
                        "--no-sandbox",  # 避免沙盒问题
                        # 保留用户数据相关的参数被移除，以保持现有登录状态
                        # "--disable-extensions",  # 保留扩展
                        # "--disable-sync",  # 保留同步
                        "--new-window",  # 在新窗口中打开
                        "--disable-prompt-on-repost",  # 禁用重新提交提示
                        "--disable-domain-reliability",  # 禁用域可靠性
                        # "--auto-open-devtools-for-tabs",  # 不自动打开开发者工具
                        "--enable-logging",  # 启用日志
                        "--v=1",  # 详细日志级别
                        "--window-name=光流助手调试窗口",  # 自定义窗口名称
                        "--disable-client-side-phishing-detection",  # 禁用钓鱼检测
                        "--disable-component-update",  # 禁用组件更新
                        "--no-pings",  # 禁用ping
                        "--no-crash-upload",  # 禁用崩溃上传
                        self.website_url  # 直接打开目标网站
                    ]

                    # 启动Chrome进程
                    self.log_message.emit(f"🚀 启动Chrome命令: {' '.join(startup_args[:8])}...")
                    
                    # 使用CREATE_NEW_PROCESS_GROUP避免继承父进程的信号处理
                    import subprocess
                    process = subprocess.Popen(
                        startup_args, 
                        creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )
                    
                    self.log_message.emit(f"🚀 已启动Chrome调试实例，PID: {process.pid}, 端口: {self.debug_port}")
                    self.log_message.emit(f"🌐 已打开目标网站: {self.website_url}")
                    
                    # 等待Chrome完全启动并监听端口
                    self.log_message.emit("⏳ 等待Chrome调试端口完全启动...")
                    max_wait_time = 15  # 最多等待15秒
                    wait_interval = 1
                    
                    for i in range(max_wait_time):
                        time.sleep(wait_interval)
                        if self.check_debug_browser():
                            self.log_message.emit(f"✅ Chrome调试端口启动成功 (等待{i+1}秒)")
                            return True
                        
                        if i == 4:
                            self.log_message.emit("💡 Chrome正在启动调试端口，请稍候...")
                        elif i == 9:
                            self.log_message.emit("⏳ 仍在等待调试端口响应...")
                    
                    self.log_message.emit(f"⚠️ Chrome调试端口启动超时，但进程已启动 (PID: {process.pid})")
                    return True  # 即使超时也返回True，让后续连接逻辑处理
                    
                else:
                    self.log_message.emit("❌ 未找到Chrome浏览器")
                    return False
            else:
                # macOS/Linux
                # 确保Chrome调试目录存在
                try:
                    os.makedirs(self.chrome_debug_dir, exist_ok=True)
                except:
                    pass
                    
                subprocess.Popen([
                    "google-chrome",
                    f"--remote-debugging-port={self.debug_port}",
                    "--remote-debugging-address=0.0.0.0",
                    f"--user-data-dir={self.chrome_debug_dir}",
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--disable-web-security",
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-extensions",
                    "--disable-default-apps",
                    "--new-window",
                    self.website_url
                ])
                return True

        except Exception as e:
            self.log_message.emit(f"❌ 启动调试浏览器失败: {str(e)}")
            return False
    
    def kill_debug_chrome_processes(self):
        """强制关闭所有Chrome调试进程"""
        try:
            if PSUTIL_AVAILABLE:
                import psutil
                closed_count = 0
                
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                            cmdline = proc.info['cmdline'] or []
                            # 关闭所有带调试端口的Chrome进程
                            if any(f'--remote-debugging-port={self.debug_port}' in arg for arg in cmdline):
                                proc.kill()  # 使用kill而不是terminate，确保进程被强制关闭
                                self.log_message.emit(f"🔄 强制关闭调试Chrome进程: PID {proc.info['pid']}")
                                closed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        pass

                if closed_count > 0:
                    self.log_message.emit(f"🔄 已强制关闭 {closed_count} 个调试Chrome进程")
                else:
                    self.log_message.emit("🔍 未找到需要关闭的调试Chrome进程")
            else:
                # 如果psutil不可用，使用系统命令
                import subprocess
                try:
                    # Windows: 关闭所有chrome进程（谨慎使用）
                    subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'], 
                                 capture_output=True, text=True, timeout=5)
                    self.log_message.emit("🔄 已尝试关闭Chrome进程")
                except:
                    pass
                    
        except Exception as e:
            self.log_message.emit(f"⚠️ 进程清理异常: {str(e)}")
        
        
    def get_recent_week_data(self) -> pd.DataFrame:
        """获取最近7天的数据（只使用更新日期列，更新日期为空的不显示）"""
        try:
            if not os.path.exists(self.avatar_list_path):
                self.log_message.emit("avatar_list.xlsx文件不存在，返回空数据")
                return pd.DataFrame()

            # 读取现有数据
            df = pd.read_excel(self.avatar_list_path)
            self.log_message.emit(f"📊 读取文件成功，总计 {len(df)} 条记录")

            # 只使用更新日期列进行筛选
            if '更新日期' not in df.columns:
                self.log_message.emit("❌ 未找到'更新日期'列，返回空数据")
                return pd.DataFrame()

            # 转换更新日期列为datetime
            df['更新日期'] = pd.to_datetime(df['更新日期'], errors='coerce')

            # 统计更新日期数据情况
            total_records = len(df)
            null_date_records = df['更新日期'].isna().sum()
            valid_date_records = total_records - null_date_records

            self.log_message.emit(f"📊 更新日期统计: 总计 {total_records} 条，有效更新日期 {valid_date_records} 条，空更新日期 {null_date_records} 条")

            if valid_date_records == 0:
                self.log_message.emit("📋 所有记录的更新日期都为空，返回空数据")
                return pd.DataFrame()

            # 计算7天前的日期（精确到天）
            seven_days_ago = datetime.now() - timedelta(days=7)
            seven_days_ago = seven_days_ago.replace(hour=0, minute=0, second=0, microsecond=0)

            self.log_message.emit(f"📅 筛选条件: {seven_days_ago.strftime('%Y-%m-%d')} 之后的更新日期")

            # 过滤条件：
            # 1. 更新日期不为空（不是NaT）
            # 2. 更新日期在最近7天内
            recent_data = df[
                (df['更新日期'].notna()) &  # 更新日期不为空
                (df['更新日期'] >= seven_days_ago)  # 在最近7天内
            ]

            self.log_message.emit(f"✅ 筛选结果: {len(recent_data)} 条最近7天有更新日期的记录")

            # 按更新日期降序排列（最新的在前面）
            if len(recent_data) > 0:
                recent_data = recent_data.sort_values('更新日期', ascending=False)
                self.log_message.emit(f"📋 数据已按更新日期降序排列")
            else:
                self.log_message.emit("📋 没有找到最近7天有更新日期的记录")

            return recent_data

        except Exception as e:
            self.log_message.emit(f"❌ 获取最近7天数据失败: {str(e)}")
            return pd.DataFrame()
    
    def get_display_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """获取需要显示的列"""
        # 根据实际文件的列名进行映射
        column_mapping = {
            "素材ID": "ID",
            "外链BOS地址": "视频URL",
            "上传人邮箱后缀": "上传人邮箱后缀",
            "拍摄演员名称": "拍摄演员名称",
            "视频版型": "视频版型",
            "场景": "场景",
            "表现形式": "表现形式",
            "服装": "服装",
            "是否上传飞影": "是否上传飞影",
            "上传时间": "上传时间",
            "更新日期": "更新日期"  # 如果存在的话
        }

        # 创建显示用的DataFrame
        display_df = pd.DataFrame()

        # 按映射关系添加列
        for original_col, display_col in column_mapping.items():
            if original_col in df.columns:
                display_df[display_col] = df[original_col]

        # 如果没有"是否上传飞影"列，添加空列
        if "是否上传飞影" not in display_df.columns:
            display_df["是否上传飞影"] = ""

        # 确保有日期列用于显示
        if "更新日期" not in display_df.columns and "上传时间" in display_df.columns:
            display_df["更新日期"] = display_df["上传时间"]

        self.log_message.emit(f"📋 显示列映射完成，包含 {len(display_df.columns)} 列")

        return display_df
    
    async def download_material_data(self) -> bool:
        """从网站下载素材数据"""
        if not PLAYWRIGHT_AVAILABLE:
            self.log_message.emit("❌ Playwright未安装，无法下载素材数据")
            return False
        
        try:
            self.log_message.emit("🚀 开始下载素材数据...")

            # 检查调试端口浏览器
            self.log_message.emit(f"🔍 检查Chrome调试端口 {self.debug_port} 是否可用...")
            if not self.check_debug_browser():
                self.log_message.emit("🔧 调试端口浏览器未运行，正在自动启动...")
                self.log_message.emit("💡 程序将自动打开Chrome浏览器窗口")

                if not self.start_debug_browser():
                    self.log_message.emit("❌ 启动调试浏览器失败")
                    return False
                else:
                    # 等待浏览器启动并检查连接，增加等待时间
                    import time
                    self.log_message.emit("⏳ 等待Chrome调试端口启动...")
                    self.log_message.emit("📋 程序将自动处理，无需手动操作")

                    # 增加等待时间到60秒，Chrome启动调试端口需要更多时间
                    max_wait_attempts = 60
                    for i in range(max_wait_attempts):
                        time.sleep(1)
                        if self.check_debug_browser():
                            self.log_message.emit(f"✅ Chrome调试端口启动成功 (等待{i+1}秒)")
                            break
                        if i == 4:
                            self.log_message.emit("💡 Chrome正在启动调试端口，请稍候...")
                        elif i == 9:
                            self.log_message.emit("⏳ 仍在等待调试端口响应...")
                        elif i == 19:
                            self.log_message.emit("⏳ 调试端口启动较慢，继续等待...")
                        elif i == 39:
                            self.log_message.emit("⏳ 延长等待时间，Chrome可能需要更多时间...")
                        elif i % 10 == 0 and i > 0:
                            self.log_message.emit(f"⏳ 等待中... ({i+1}/{max_wait_attempts})")
                    else:
                        self.log_message.emit("⚠️ 调试端口启动超时，但将继续尝试连接")
            else:
                self.log_message.emit("✅ 检测到调试端口浏览器正在运行")

            async with async_playwright() as p:
                # 多次尝试连接到调试端口的浏览器
                browser = None
                max_connection_attempts = 5
                
                for attempt in range(max_connection_attempts):
                    try:
                        self.log_message.emit(f"🔗 尝试连接到调试端口 {self.debug_port}... (第{attempt+1}次)")
                        # 使用IPv4地址避免IPv6连接问题
                        browser = await p.chromium.connect_over_cdp(f"http://127.0.0.1:{self.debug_port}")
                        self.log_message.emit("✅ 成功连接到现有浏览器实例")
                        break
                    except Exception as e:
                        self.log_message.emit(f"⚠️ 连接调试端口失败 (第{attempt+1}次): {str(e)}")
                        if attempt < max_connection_attempts - 1:
                            self.log_message.emit("⏳ 等待3秒后重试...")
                            await asyncio.sleep(3)
                        else:
                            self.log_message.emit("🚀 启动新的浏览器实例...")
                            # 如果连接失败，启动新的浏览器实例
                            browser = await p.chromium.launch(
                                headless=False,
                                args=[
                                    f"--remote-debugging-port={self.debug_port}",
                                    "--disable-web-security",
                                    "--disable-features=VizDisplayCompositor"
                                ]
                            )
                
                if not browser:
                    self.log_message.emit("❌ 无法连接或启动浏览器")
                    return False

                # 获取或创建页面
                contexts = browser.contexts
                if contexts:
                    # 使用现有的上下文
                    context = contexts[0]
                    pages = context.pages
                    if pages:
                        page = pages[0]
                        self.log_message.emit("📄 使用现有页面")

                        # 检查当前页面URL
                        current_url = page.url
                        self.log_message.emit(f"📄 当前页面URL: {current_url}")

                        # 如果不在目标页面，则导航
                        if self.website_url not in current_url:
                            self.log_message.emit(f"📱 导航到目标页面: {self.website_url}")
                            try:
                                await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)
                                self.log_message.emit("✅ 页面导航成功")
                            except Exception as e:
                                self.log_message.emit(f"⚠️ 页面导航失败: {str(e)}")
                                # 尝试创建新页面而不是刷新
                                try:
                                    page = await context.new_page()
                                    await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)
                                    self.log_message.emit("✅ 在新页面中成功打开目标网站")
                                except Exception as e2:
                                    self.log_message.emit(f"❌ 新页面导航也失败: {str(e2)}")
                                    return False
                        else:
                            self.log_message.emit("✅ 已在目标页面，无需导航")
                    else:
                        page = await context.new_page()
                        self.log_message.emit("📄 在现有上下文中创建新页面")
                        await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)
                else:
                    # 创建新的上下文和页面
                    context = await browser.new_context()
                    page = await context.new_page()
                    self.log_message.emit("📄 创建新的上下文和页面")
                    await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)

                # 等待页面稳定并验证页面可用性
                await page.wait_for_timeout(3000)

                # 验证页面是否真正可用
                try:
                    # 检查页面是否已关闭
                    if page.is_closed():
                        self.log_message.emit("❌ 页面已关闭，无法继续操作")
                        return False

                    # 检查页面URL是否正确
                    current_url = page.url
                    if "chrome-extension://" in current_url or "about:" in current_url:
                        self.log_message.emit(f"⚠️ 页面URL异常: {current_url}，尝试重新导航")
                        await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)
                        await page.wait_for_timeout(2000)

                    self.log_message.emit("✅ 页面加载完成")
                except Exception as e:
                    self.log_message.emit(f"❌ 页面验证失败: {str(e)}")
                    return False
                
                # 1. 点击素材创建日期的选择框
                self.log_message.emit("📅 设置素材创建日期...")
                date_selector = ".el-date-editor.el-range-editor"

                try:
                    # 等待元素出现并确保可点击
                    await page.wait_for_selector(date_selector, timeout=10000, state="visible")
                    await page.click(date_selector)
                    await page.wait_for_timeout(1000)
                except Exception as e:
                    self.log_message.emit(f"❌ 点击日期选择框失败: {str(e)}")
                    return False
                
                # 选择"最近一年"选项
                try:
                    # 查找并点击"最近一年"选项
                    recent_year_option = await page.wait_for_selector("text=最近一年", timeout=5000)
                    await recent_year_option.click()
                    self.log_message.emit("✅ 已选择最近一年")
                    await page.wait_for_timeout(1000)
                except Exception as e:
                    self.log_message.emit(f"⚠️ 选择最近一年失败: {str(e)}")
                
                # 2. 点击右侧的下拉选择框（剪辑素材库 -> 拍摄素材库）
                self.log_message.emit("📂 设置素材库类型...")
                try:
                    # 查找第二个下拉框
                    dropdown_selectors = await page.query_selector_all(".el-select")
                    if len(dropdown_selectors) >= 2:
                        await dropdown_selectors[1].click()
                        await page.wait_for_timeout(1000)
                        
                        # 选择"拍摄素材库"
                        shooting_option = await page.wait_for_selector("text=拍摄素材库", timeout=5000)
                        await shooting_option.click()
                        self.log_message.emit("✅ 已选择拍摄素材库")
                        await page.wait_for_timeout(1000)
                    else:
                        self.log_message.emit("⚠️ 未找到素材库选择框")
                except Exception as e:
                    self.log_message.emit(f"⚠️ 设置素材库类型失败: {str(e)}")
                
                # 3. 点击导出按钮
                self.log_message.emit("📤 开始导出数据...")
                try:
                    export_button = await page.wait_for_selector(".el-button.el-button--primary", timeout=5000)
                    
                    # 设置下载监听
                    download_path = None
                    
                    async def handle_download(download):
                        nonlocal download_path
                        # 保存到临时目录
                        download_path = os.path.join(self.temp_dir, f"material_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
                        self.log_message.emit(f"📥 开始下载文件到: {download_path}")
                        await download.save_as(download_path)
                        self.log_message.emit(f"✅ 文件下载完成: {download_path}")
                    
                    page.on("download", handle_download)
                    
                    # 点击导出按钮
                    await export_button.click()
                    self.log_message.emit("✅ 已点击导出按钮，等待下载...")
                    
                    # 循环检测下载是否完成，最长等待60秒
                    download_timeout = 60  # 最大等待时间60秒
                    check_interval = 2  # 每2秒检查一次
                    elapsed_time = 0
                    
                    while elapsed_time < download_timeout:
                        await page.wait_for_timeout(check_interval * 1000)
                        elapsed_time += check_interval
                        
                        # 检查下载文件是否存在
                        if download_path and os.path.exists(download_path):
                            # 检查文件大小是否稳定（确保下载完成）
                            file_size = os.path.getsize(download_path)
                            if file_size > 0:
                                self.log_message.emit(f"📁 检测到下载文件，大小: {file_size} 字节")
                                # 再等待2秒确保文件写入完成
                                await page.wait_for_timeout(2000)
                                break
                        
                        # 每10秒提供一次进度反馈
                        if elapsed_time % 10 == 0:
                            self.log_message.emit(f"⏳ 等待下载中... ({elapsed_time}/{download_timeout}秒)")
                    else:
                        self.log_message.emit(f"⏰ 下载等待超时 ({download_timeout}秒)")
                    
                    await browser.close()
                    
                    if download_path and os.path.exists(download_path):
                        final_size = os.path.getsize(download_path)
                        self.log_message.emit(f"✅ 素材数据下载成功，文件大小: {final_size} 字节")
                        return await self.process_downloaded_data(download_path)
                    else:
                        self.log_message.emit("❌ 下载失败，未找到下载文件或文件为空")
                        self.log_message.emit(f"📋 下载路径: {download_path}")
                        self.log_message.emit(f"📋 文件存在: {os.path.exists(download_path) if download_path else False}")
                        return False
                        
                except Exception as e:
                    self.log_message.emit(f"❌ 导出失败: {str(e)}")
                    await browser.close()
                    return False
                    
        except Exception as e:
            self.log_message.emit(f"❌ 下载素材数据失败: {str(e)}")
            return False
    
    async def process_downloaded_data(self, downloaded_file_path: str) -> bool:
        """处理下载的数据文件"""
        try:
            self.log_message.emit("🔄 开始处理下载的数据...")
            
            # 读取下载的文件
            new_df = pd.read_excel(downloaded_file_path)
            self.log_message.emit(f"📊 下载文件包含 {len(new_df)} 条记录")
            
            # 根据A列（素材ID）进行去重
            if '素材ID' in new_df.columns:
                original_count = len(new_df)
                new_df = new_df.drop_duplicates(subset=['素材ID'], keep='first')
                dedup_count = len(new_df)
                self.log_message.emit(f"🔄 去重处理: {original_count} -> {dedup_count} 条记录")
            else:
                self.log_message.emit("⚠️ 未找到素材ID列，跳过去重")
            
            # 处理现有的avatar_list.xlsx文件
            return await self.merge_with_existing_data(new_df)
            
        except Exception as e:
            self.log_message.emit(f"❌ 处理下载数据失败: {str(e)}")
            return False
    
    async def merge_with_existing_data(self, new_df: pd.DataFrame) -> bool:
        """与现有数据合并"""
        try:
            self.log_message.emit("🔄 开始与现有数据合并...")
            
            # 读取现有数据
            if os.path.exists(self.avatar_list_path):
                existing_df = pd.read_excel(self.avatar_list_path)
                self.log_message.emit(f"📊 现有数据包含 {len(existing_df)} 条记录")
            else:
                existing_df = pd.DataFrame()
                self.log_message.emit("📊 创建新的数据文件")
            
            # 列名映射
            column_mapping = {
                '素材ID': 'ID',
                '外链BOS地址': '视频URL'
            }
            
            # 重命名新数据的列
            new_df_renamed = new_df.rename(columns=column_mapping)
            
            # 添加默认列
            if '是否上传飞影' not in new_df_renamed.columns:
                new_df_renamed['是否上传飞影'] = ''
            
            # 添加更新日期
            new_df_renamed['更新日期'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 如果现有数据为空，直接使用新数据
            if existing_df.empty:
                result_df = new_df_renamed
                new_count = len(result_df)
            else:
                # 找出新增的记录（ID不存在于现有数据中）
                if 'ID' in existing_df.columns and 'ID' in new_df_renamed.columns:
                    existing_ids = set(existing_df['ID'].astype(str))
                    new_records = new_df_renamed[~new_df_renamed['ID'].astype(str).isin(existing_ids)]
                    new_count = len(new_records)
                    
                    if new_count > 0:
                        # 将新记录添加到现有数据的最上面
                        result_df = pd.concat([new_records, existing_df], ignore_index=True)
                        self.log_message.emit(f"✅ 新增 {new_count} 条记录")
                    else:
                        result_df = existing_df
                        self.log_message.emit("ℹ️ 没有新增记录")
                else:
                    self.log_message.emit("⚠️ 缺少ID列，无法进行增量更新")
                    return False
            
            # 保存更新后的数据
            result_df.to_excel(self.avatar_list_path, index=False)
            self.log_message.emit(f"💾 数据已保存，总计 {len(result_df)} 条记录")
            
            # 清理临时文件
            try:
                if os.path.exists(downloaded_file_path):
                    os.remove(downloaded_file_path)
                    self.log_message.emit("🗑️ 已清理临时文件")
            except Exception as e:
                self.log_message.emit(f"⚠️ 清理临时文件失败: {str(e)}")
            
            self.update_completed.emit(True, f"素材更新完成，新增 {new_count} 条记录")
            return True
            
        except Exception as e:
            self.log_message.emit(f"❌ 数据合并失败: {str(e)}")
            self.update_completed.emit(False, f"数据合并失败: {str(e)}")
            return False


class MaterialUpdateWorker(QThread):
    """素材更新工作线程"""
    
    def __init__(self, material_manager):
        super().__init__()
        self.material_manager = material_manager
    
    def run(self):
        """运行素材更新"""
        try:
            # 在新的事件循环中运行异步任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            success = loop.run_until_complete(
                self.material_manager.download_material_data()
            )
            
            loop.close()
            
        except Exception as e:
            self.material_manager.log_message.emit(f"❌ 素材更新线程异常: {str(e)}")
            self.material_manager.update_completed.emit(False, f"更新失败: {str(e)}")
