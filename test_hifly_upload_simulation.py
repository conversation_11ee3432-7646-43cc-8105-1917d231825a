#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模拟飞影数字人上传测试
使用指定的视频文件和空命名进行测试
"""

import os
import sys
import json
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def simulate_video_data_creation():
    """模拟创建视频数据"""
    print("=== 模拟视频数据创建 ===")
    
    # 模拟用户输入
    test_video_path = "D:\\project\\guangliu02\\test\\1.mp4"
    actor_name_input = ""  # 用户留空
    
    print(f"📹 测试视频文件: {test_video_path}")
    print(f"🎭 用户输入的演员名称: '{actor_name_input}' (空)")
    
    # 检查文件是否存在
    if not os.path.exists(test_video_path):
        print(f"❌ 测试文件不存在: {test_video_path}")
        return None
    
    # 模拟从文件名提取演员名称的逻辑
    filename = os.path.basename(test_video_path)
    name_without_ext = os.path.splitext(filename)[0]
    
    print(f"📁 文件名: {filename}")
    print(f"📝 去除扩展名: {name_without_ext}")
    
    # 模拟演员名称处理逻辑
    if actor_name_input.strip():
        final_actor_name = actor_name_input.strip()
        print(f"✅ 使用用户输入的演员名称: '{final_actor_name}'")
    else:
        # 尝试从文件名提取
        extracted_name = extract_name_from_filename(filename)
        if extracted_name:
            final_actor_name = extracted_name
            print(f"🔍 从文件名提取的演员名称: '{final_actor_name}'")
        else:
            # 使用默认值
            final_actor_name = "演员"
            print(f"⚠️ 无法提取演员名称，使用默认值: '{final_actor_name}'")
    
    # 模拟生成视频数据
    video_data = {
        "ID": "99999",  # 模拟ID
        "视频URL": f"https://example.com/videos/{filename}",
        "拍摄演员名称": final_actor_name,
        "视频版型": "竖版",
        "场景": "测试场景",
        "表现形式": "坐播",
        "服装": "休闲装",
        "是否上传飞影": "",
        "更新日期": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    print(f"\n📊 生成的视频数据:")
    for key, value in video_data.items():
        print(f"  {key}: {value}")
    
    return video_data

def extract_name_from_filename(filename):
    """从文件名提取姓名（复制原始逻辑）"""
    import re
    
    # 移除扩展名
    name_without_ext = os.path.splitext(filename)[0]
    
    # 按照常见分隔符分割
    parts = re.split(r'[-_\s]+', name_without_ext)
    
    # 查找单独的字母（通常是姓名）
    for part in parts:
        if len(part) == 1 and part.isalpha():
            return part
    
    return ""

def simulate_hifly_naming():
    """模拟飞影命名逻辑"""
    print(f"\n=== 模拟飞影数字人命名 ===")
    
    video_data = simulate_video_data_creation()
    if not video_data:
        return
    
    # 模拟当前的命名逻辑
    video_id = video_data.get("ID")
    video_url = video_data.get("视频URL")
    actor_name = video_data.get("拍摄演员名称")
    
    print(f"\n🔍 命名参数:")
    print(f"  视频ID: {video_id}")
    print(f"  视频URL: {video_url}")
    print(f"  演员名称: '{actor_name}'")
    
    # 当前的命名格式：演员名称-ID-演员
    title = f"{actor_name}-{video_id}-演员"
    
    print(f"\n🎯 生成的数字人名称: '{title}'")
    
    # 分析可能的问题
    print(f"\n🔍 命名分析:")
    print(f"  - 基础格式: {{演员名称}}-{{ID}}-演员")
    print(f"  - 实际结果: {title}")
    print(f"  - 字符数量: {len(title)} 个字符")
    
    # 检查可能的问题源
    potential_issues = []
    
    if actor_name == "演员":
        potential_issues.append("使用了默认演员名称，可能导致重复")
    
    if "-演员" in title:
        potential_issues.append("末尾的'-演员'可能被飞影API处理")
    
    if len(title) > 20:
        potential_issues.append("名称过长，可能被截断或添加标识符")
    
    print(f"\n⚠️ 潜在问题:")
    for issue in potential_issues:
        print(f"  - {issue}")
    
    return title

def simulate_url_analysis():
    """模拟URL分析"""
    print(f"\n=== 模拟视频URL分析 ===")
    
    # 模拟真实的视频URL格式
    test_video_url = "https://example.com/videos/1.mp4"
    
    print(f"📹 模拟的视频URL: {test_video_url}")
    
    # 分析URL中可能影响命名的因素
    from urllib.parse import urlparse
    parsed_url = urlparse(test_video_url)
    
    print(f"🔍 URL分析:")
    print(f"  域名: {parsed_url.netloc}")
    print(f"  路径: {parsed_url.path}")
    print(f"  文件名: {os.path.basename(parsed_url.path)}")
    
    # 检查文件名中的特殊字符
    filename = os.path.basename(parsed_url.path)
    special_chars = [char for char in filename if not char.isalnum() and char != '.']
    
    print(f"  特殊字符: {special_chars}")
    
    if special_chars:
        print(f"⚠️ 文件名包含特殊字符，可能影响飞影API的处理")
    
    return test_video_url

def test_different_scenarios():
    """测试不同场景"""
    print(f"\n=== 测试不同命名场景 ===")
    
    scenarios = [
        {
            "name": "场景1: 文件名为数字",
            "filename": "1.mp4",
            "actor_input": "",
            "expected_issues": ["无法提取演员名称", "使用默认值"]
        },
        {
            "name": "场景2: 文件名包含中文",
            "filename": "张三.mp4", 
            "actor_input": "",
            "expected_issues": ["可能提取到中文名"]
        },
        {
            "name": "场景3: 用户指定演员名称",
            "filename": "1.mp4",
            "actor_input": "测试演员",
            "expected_issues": ["正常命名"]
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}")
        print(f"  文件名: {scenario['filename']}")
        print(f"  用户输入: '{scenario['actor_input']}'")
        
        # 模拟处理逻辑
        if scenario['actor_input'].strip():
            actor_name = scenario['actor_input'].strip()
        else:
            extracted = extract_name_from_filename(scenario['filename'])
            actor_name = extracted if extracted else "演员"
        
        title = f"{actor_name}-99999-演员"
        
        print(f"  生成名称: '{title}'")
        print(f"  预期问题: {scenario['expected_issues']}")

def analyze_hifly_api_behavior():
    """分析飞影API可能的行为"""
    print(f"\n=== 分析飞影API可能的行为 ===")
    
    print(f"🔍 飞影API可能的处理逻辑:")
    print(f"  1. 接收数字人名称: '演员-99999-演员'")
    print(f"  2. 解析视频URL: 'https://example.com/videos/1.mp4'")
    print(f"  3. 可能的额外处理:")
    print(f"     - 从视频文件名提取信息")
    print(f"     - 添加唯一标识符避免重名")
    print(f"     - 根据视频内容分析添加标签")
    print(f"     - 自动添加版本号或序号")
    
    print(f"\n⚠️ 可能导致额外字母的原因:")
    print(f"  1. 重名处理: '演员-99999-演员' → '演员-99999-演员_v2'")
    print(f"  2. 文件名影响: 从'1.mp4'提取 → '演员-99999-演员1'")
    print(f"  3. 自动标签: 根据内容 → '演员-99999-演员_male'")
    print(f"  4. 系统标识: 内部处理 → '演员-99999-演员_abc'")

def generate_test_recommendations():
    """生成测试建议"""
    print(f"\n=== 测试建议 ===")
    
    print(f"🧪 建议的测试步骤:")
    print(f"  1. 准备测试文件:")
    print(f"     - 确保 D:\\project\\guangliu02\\test\\1.mp4 存在")
    print(f"     - 文件应该是有效的视频文件")
    
    print(f"\n  2. 执行上传测试:")
    print(f"     - 启动应用程序")
    print(f"     - 切换到视频管理页面")
    print(f"     - 添加测试视频记录")
    print(f"     - 演员名称字段留空")
    print(f"     - 执行飞影上传")
    
    print(f"\n  3. 观察结果:")
    print(f"     - 记录应用程序中显示的数字人名称")
    print(f"     - 访问飞影前台页面: https://api.hifly.cc/hifly.html")
    print(f"     - 对比前台显示的名称与应用程序中的名称")
    print(f"     - 记录多出来的字母内容")
    
    print(f"\n  4. 分析差异:")
    print(f"     - 确定额外字母的来源")
    print(f"     - 检查是否与文件名相关")
    print(f"     - 验证是否为飞影API自动添加")

def main():
    """主函数"""
    print("🧪 飞影数字人上传命名测试")
    print("=" * 60)
    
    # 模拟飞影命名逻辑
    title = simulate_hifly_naming()
    
    # 模拟URL分析
    simulate_url_analysis()
    
    # 测试不同场景
    test_different_scenarios()
    
    # 分析API行为
    analyze_hifly_api_behavior()
    
    # 生成测试建议
    generate_test_recommendations()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    print(f"🎯 关键发现:")
    print(f"  1. 使用'1.mp4'文件，命名留空")
    print(f"  2. 预期生成的数字人名称: '演员-99999-演员'")
    print(f"  3. 可能的问题源: 文件名、API处理、重名避免")
    
    print(f"\n💡 下一步行动:")
    print(f"  1. 执行实际的上传测试")
    print(f"  2. 对比预期名称与实际显示")
    print(f"  3. 根据结果调整命名逻辑")
    
    return title

if __name__ == "__main__":
    main()
