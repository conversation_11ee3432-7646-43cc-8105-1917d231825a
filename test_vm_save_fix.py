#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试视频管理模块保存修复
验证ID修改后的保存逻辑是否正确
"""

import os
import sys
import re

def test_save_logic_fix():
    """测试保存逻辑修复"""
    print("=== 测试视频管理保存逻辑修复 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查修复的关键代码
        fixes = [
            # 原始ID记录
            (r'self\.vm_original_ids = \{\}', "原始ID记录字典", True),
            (r'self\.vm_original_ids\[row_idx\] = value', "记录原始ID", True),
            
            # 保存时使用原始ID
            (r'if row in self\.vm_original_ids:', "检查原始ID存在", True),
            (r'record_id = self\.vm_original_ids\[row\]', "使用原始ID", True),
            
            # ID更新后的处理
            (r'if col_name == "ID":', "ID修改检查", True),
            (r'self\.vm_original_ids\[row_idx\] = new_value', "更新原始ID", True),
        ]
        
        print("🔍 检查修复代码:")
        all_fixes_found = True
        
        for pattern, description, should_exist in fixes:
            matches = re.findall(pattern, content)
            count = len(matches)
            
            if should_exist:
                success = count > 0
                status = f"✅ 找到 {count} 处" if success else "❌ 未找到"
            else:
                success = count == 0
                status = f"✅ 已移除" if success else f"❌ 仍有 {count} 处"
            
            print(f"  {description}: {status}")
            if not success:
                all_fixes_found = False
        
        return all_fixes_found
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_save_workflow():
    """测试保存工作流程"""
    print("\n=== 测试保存工作流程 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查保存工作流程的完整性
        workflow_steps = [
            (r'on_vm_table_item_changed', "表格变更监听", True),
            (r'vm_pending_saves', "待保存队列", True),
            (r'vm_save_timer', "防抖定时器", True),
            (r'execute_pending_vm_saves', "批量保存执行", True),
            (r'save_vm_table_change', "单个变更保存", True),
            (r'update_record_field', "Excel记录更新", True),
        ]
        
        print("🔍 检查保存工作流程:")
        workflow_complete = True
        
        for pattern, description, should_exist in workflow_steps:
            matches = re.findall(pattern, content)
            count = len(matches)
            
            if should_exist:
                success = count > 0
                status = f"✅ 找到 {count} 处" if success else "❌ 未找到"
            else:
                success = count == 0
                status = f"✅ 已移除" if success else f"❌ 仍有 {count} 处"
            
            print(f"  {description}: {status}")
            if not success:
                workflow_complete = False
        
        return workflow_complete
        
    except Exception as e:
        print(f"❌ 工作流程测试失败: {str(e)}")
        return False

def explain_fix():
    """解释修复方案"""
    print("\n=== 修复方案说明 ===")
    
    print("🔍 原始问题:")
    print("  用户修改ID: 38042 → 99999")
    print("  系统尝试保存: 用新ID(99999)查找Excel记录")
    print("  Excel中只有: 旧ID(38042)的记录")
    print("  结果: ❌ 未找到ID为 99999 的记录")
    
    print(f"\n✅ 修复方案:")
    print(f"  1. 记录原始ID:")
    print(f"     self.vm_original_ids[row] = original_id")
    print(f"  ")
    print(f"  2. 保存时使用原始ID:")
    print(f"     if row in self.vm_original_ids:")
    print(f"         record_id = self.vm_original_ids[row]  # 使用原始ID")
    print(f"  ")
    print(f"  3. 保存成功后更新原始ID:")
    print(f"     if col_name == 'ID':")
    print(f"         self.vm_original_ids[row] = new_value")
    
    print(f"\n🎯 修复效果:")
    print(f"  用户修改ID: 38042 → 99999")
    print(f"  系统查找记录: 用原始ID(38042)查找Excel")
    print(f"  找到记录: ✅ 找到ID为 38042 的记录")
    print(f"  更新记录: 将ID从 38042 改为 99999")
    print(f"  更新原始ID: 记录新的原始ID为 99999")

def compare_with_voice_module():
    """对比声音克隆模块"""
    print("\n=== 对比声音克隆模块 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查声音克隆模块是否有保存机制
        voice_save_features = [
            (r'voice_table.*itemChanged', "声音表格变更监听", False),
            (r'voice.*pending_saves', "声音待保存队列", False),
            (r'voice.*save_timer', "声音保存定时器", False),
        ]
        
        print("🔍 声音克隆模块保存机制:")
        for pattern, description, should_exist in voice_save_features:
            matches = re.findall(pattern, content)
            count = len(matches)
            
            if should_exist:
                status = f"✅ 找到 {count} 处" if count > 0 else "❌ 未找到"
            else:
                status = f"ℹ️ 未找到" if count == 0 else f"⚠️ 意外找到 {count} 处"
            
            print(f"  {description}: {status}")
        
        print(f"\n💡 模块对比:")
        print(f"  视频管理模块:")
        print(f"    ✅ 有自动保存机制")
        print(f"    ✅ 连接itemChanged信号")
        print(f"    ✅ 防抖保存定时器")
        print(f"    ✅ 批量保存到Excel")
        print(f"  ")
        print(f"  声音克隆模块:")
        print(f"    ❌ 无自动保存机制")
        print(f"    ❌ 未连接itemChanged信号")
        print(f"    ℹ️ 可能是只读数据展示")
        print(f"    ℹ️ 数据来源于API而非Excel")
        
    except Exception as e:
        print(f"❌ 对比测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("🧪 视频管理保存逻辑修复测试")
    print("=" * 60)
    
    # 测试保存逻辑修复
    save_fix_ok = test_save_logic_fix()
    
    # 测试保存工作流程
    workflow_ok = test_save_workflow()
    
    # 解释修复方案
    explain_fix()
    
    # 对比声音克隆模块
    compare_with_voice_module()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    print(f"🎯 测试结果:")
    print(f"  - 保存逻辑修复: {'✅ 通过' if save_fix_ok else '❌ 失败'}")
    print(f"  - 保存工作流程: {'✅ 完整' if workflow_ok else '❌ 不完整'}")
    
    overall_success = save_fix_ok and workflow_ok
    
    if overall_success:
        print(f"\n🎉 保存逻辑修复成功！")
        print(f"\n📋 修复总结:")
        print(f"  ✅ 添加了原始ID记录机制")
        print(f"  ✅ 修改了保存时的ID查找逻辑")
        print(f"  ✅ 增加了ID更新后的处理")
        print(f"  ✅ 保持了防抖保存机制")
        
        print(f"\n🚀 用户现在可以:")
        print(f"  - 修改ID并成功保存")
        print(f"  - 修改其他字段并成功保存")
        print(f"  - 享受防抖保存的流畅体验")
        print(f"  - 看到明确的保存成功反馈")
        
        print(f"\n💡 技术改进:")
        print(f"  - 解决了ID修改后无法保存的问题")
        print(f"  - 保持了Excel数据的一致性")
        print(f"  - 提高了用户编辑体验")
        print(f"  - 增强了数据管理的灵活性")
    else:
        print(f"\n❌ 部分修复可能不完整，需要进一步检查")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
