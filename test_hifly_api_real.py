#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真实的飞影API测试
使用指定视频文件进行实际的数字人创建测试
"""

import os
import sys
import time
import requests
import json
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class HiflyAPITester:
    """飞影API测试器"""
    
    def __init__(self, token: str):
        self.token = token
        self.base_url = "https://hfw-api.hifly.cc"
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
    def test_connection(self):
        """测试API连接"""
        print("🔗 测试飞影API连接...")
        
        try:
            url = f"{self.base_url}/api/v2/hifly/account/credit"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            print(f"📡 请求URL: {url}")
            print(f"📊 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API连接成功")
                print(f"📋 账户信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return True
            else:
                print(f"❌ API连接失败: {response.status_code}")
                print(f"📄 响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 连接测试失败: {str(e)}")
            return False
    
    def upload_video_file(self, video_path: str):
        """上传视频文件到飞影"""
        print(f"\n📹 上传视频文件: {video_path}")
        
        if not os.path.exists(video_path):
            print(f"❌ 视频文件不存在: {video_path}")
            return None
        
        try:
            url = f"{self.base_url}/api/v2/hifly/upload/video"
            
            with open(video_path, 'rb') as video_file:
                files = {'file': video_file}
                headers = {"Authorization": f"Bearer {self.token}"}
                
                print(f"📡 上传URL: {url}")
                print(f"📁 文件大小: {os.path.getsize(video_path)} bytes")
                
                response = requests.post(url, files=files, headers=headers, timeout=60)
                
                print(f"📊 上传响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 视频上传成功")
                    print(f"📋 上传结果: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    video_url = data.get('url') or data.get('video_url') or data.get('file_url')
                    if video_url:
                        print(f"🔗 视频URL: {video_url}")
                        return video_url
                    else:
                        print(f"⚠️ 未找到视频URL字段")
                        return None
                else:
                    print(f"❌ 视频上传失败: {response.status_code}")
                    print(f"📄 响应内容: {response.text}")
                    return None
                    
        except Exception as e:
            print(f"❌ 视频上传异常: {str(e)}")
            return None
    
    def create_avatar_by_video(self, title: str, video_url: str):
        """使用视频创建数字人"""
        print(f"\n🎭 创建数字人...")
        print(f"📝 数字人名称: '{title}'")
        print(f"🔗 视频URL: {video_url}")
        
        try:
            url = f"{self.base_url}/api/v2/hifly/avatar/create_by_video"
            
            payload = {
                "title": title,
                "video_url": video_url
            }
            
            print(f"📡 请求URL: {url}")
            print(f"📋 请求数据: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            
            response = requests.post(url, json=payload, headers=self.headers, timeout=30)
            
            print(f"📊 创建响应状态: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 数字人创建请求成功")
                print(f"📋 创建结果: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                task_id = data.get('task_id') or data.get('id')
                if task_id:
                    print(f"🆔 任务ID: {task_id}")
                    return task_id
                else:
                    print(f"⚠️ 未找到任务ID字段")
                    return None
            else:
                print(f"❌ 数字人创建失败: {response.status_code}")
                print(f"📄 响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 数字人创建异常: {str(e)}")
            return None
    
    def check_avatar_status(self, task_id: str):
        """查询数字人创建状态"""
        print(f"\n🔍 查询数字人状态...")
        print(f"🆔 任务ID: {task_id}")
        
        try:
            url = f"{self.base_url}/api/v2/hifly/avatar/task"
            params = {"task_id": task_id}
            
            print(f"📡 查询URL: {url}")
            print(f"📋 查询参数: {params}")
            
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            
            print(f"📊 查询响应状态: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 状态查询成功")
                print(f"📋 状态信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                status = data.get('status')
                avatar_id = data.get('avatar_id') or data.get('avatar')
                name = data.get('name') or data.get('title')
                
                print(f"📊 任务状态: {status}")
                if avatar_id:
                    print(f"🎭 数字人ID: {avatar_id}")
                if name:
                    print(f"📝 数字人名称: '{name}'")
                
                return data
            else:
                print(f"❌ 状态查询失败: {response.status_code}")
                print(f"📄 响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 状态查询异常: {str(e)}")
            return None
    
    def wait_for_completion(self, task_id: str, max_wait_time: int = 300):
        """等待数字人创建完成"""
        print(f"\n⏳ 等待数字人创建完成...")
        print(f"🕐 最大等待时间: {max_wait_time}秒")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            status_data = self.check_avatar_status(task_id)
            
            if status_data:
                status = status_data.get('status')
                
                if status == 'completed' or status == 'success':
                    print(f"🎉 数字人创建完成！")
                    return status_data
                elif status == 'failed' or status == 'error':
                    print(f"💔 数字人创建失败")
                    return status_data
                else:
                    print(f"⏳ 创建中... 状态: {status}")
            
            print(f"😴 等待30秒后重新查询...")
            time.sleep(30)
        
        print(f"⏰ 等待超时，创建可能仍在进行中")
        return None

def get_api_token():
    """获取API Token"""
    # 尝试从环境变量获取
    token = os.environ.get('HIFLY_API_TOKEN')
    if token:
        print(f"🔑 从环境变量获取Token: {token[:10]}...")
        return token

    # 尝试从应用配置文件获取
    config_files = [
        'config/settings.json',
        'config/hifly_config.json',
        'hifly_config.json',
        'config.json'
    ]

    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 尝试多个可能的token字段名
                    token = (config.get('hifly_token') or
                            config.get('hifly_api_token') or
                            config.get('token') or
                            config.get('api_token'))
                    if token and token != "test_token_here":
                        print(f"🔑 从配置文件获取Token: {config_file}")
                        return token
            except Exception as e:
                print(f"⚠️ 读取配置文件失败: {config_file}, {e}")

    # 提示用户输入token
    print(f"🔑 请输入飞影API Token:")
    print(f"💡 您可以从 https://api.hifly.cc/hifly.html 获取Token")
    token = input("Token: ").strip()

    if token:
        print(f"✅ 使用用户输入的Token")
        return token

    print(f"❌ 未提供有效的Token")
    return None

def simulate_naming_logic(video_path: str, user_input_name: str = ""):
    """模拟当前的命名逻辑"""
    print(f"\n🎯 模拟命名逻辑...")
    
    filename = os.path.basename(video_path)
    print(f"📁 文件名: {filename}")
    
    # 模拟演员名称提取
    if user_input_name.strip():
        actor_name = user_input_name.strip()
        print(f"👤 使用用户输入: '{actor_name}'")
    else:
        # 从文件名提取（简化版）
        name_without_ext = os.path.splitext(filename)[0]
        if name_without_ext.isdigit():
            actor_name = "演员"  # 默认值
            print(f"👤 文件名为数字，使用默认值: '{actor_name}'")
        else:
            actor_name = name_without_ext
            print(f"👤 从文件名提取: '{actor_name}'")
    
    # 模拟ID（实际使用时会是真实的ID）
    video_id = "99999"
    
    # 生成标题
    title = f"{actor_name}-{video_id}-演员"
    print(f"📝 生成的数字人名称: '{title}'")
    
    return title

def main():
    """主测试函数"""
    print("🧪 飞影API真实测试")
    print("=" * 60)
    
    # 测试参数
    test_video_path = "D:\\project\\guangliu02\\test\\1.mp4"
    user_input_name = ""  # 命名留空
    
    print(f"📹 测试视频: {test_video_path}")
    print(f"📝 用户命名: '{user_input_name}' (留空)")
    
    # 检查视频文件
    if not os.path.exists(test_video_path):
        print(f"❌ 测试视频文件不存在: {test_video_path}")
        print(f"💡 请确保文件存在后重新运行测试")
        return False
    
    # 获取API Token
    token = get_api_token()
    if not token or token == "your_test_token_here":
        print(f"❌ 请设置有效的飞影API Token")
        print(f"💡 方法1: 设置环境变量 HIFLY_API_TOKEN")
        print(f"💡 方法2: 创建配置文件 hifly_config.json")
        print(f"💡 方法3: 修改脚本中的test_token变量")
        return False
    
    # 创建API测试器
    tester = HiflyAPITester(token)
    
    # 1. 测试API连接
    if not tester.test_connection():
        print(f"❌ API连接失败，无法继续测试")
        return False
    
    # 2. 模拟命名逻辑
    title = simulate_naming_logic(test_video_path, user_input_name)
    
    # 3. 上传视频文件
    video_url = tester.upload_video_file(test_video_path)
    if not video_url:
        print(f"❌ 视频上传失败，无法继续测试")
        return False
    
    # 4. 创建数字人
    task_id = tester.create_avatar_by_video(title, video_url)
    if not task_id:
        print(f"❌ 数字人创建失败")
        return False
    
    # 5. 等待创建完成
    final_result = tester.wait_for_completion(task_id)
    
    # 6. 分析结果
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果分析")
    print(f"=" * 60)
    
    print(f"🎯 输入信息:")
    print(f"  视频文件: {os.path.basename(test_video_path)}")
    print(f"  用户命名: '{user_input_name}' (留空)")
    print(f"  生成标题: '{title}'")
    
    if final_result:
        actual_name = final_result.get('name') or final_result.get('title')
        avatar_id = final_result.get('avatar_id') or final_result.get('avatar')
        
        print(f"\n🎭 创建结果:")
        print(f"  数字人ID: {avatar_id}")
        print(f"  实际名称: '{actual_name}'")
        
        if actual_name and actual_name != title:
            print(f"\n⚠️ 名称差异分析:")
            print(f"  预期名称: '{title}'")
            print(f"  实际名称: '{actual_name}'")
            
            # 分析差异
            if actual_name.startswith(title):
                extra_chars = actual_name[len(title):]
                print(f"  额外字符: '{extra_chars}'")
                print(f"  可能原因: API自动添加后缀")
            else:
                print(f"  完全不同的命名规则")
        else:
            print(f"✅ 名称与预期一致")
        
        print(f"\n💡 建议访问飞影前台查看:")
        print(f"  🔗 https://api.hifly.cc/hifly.html")
        print(f"  🔍 查找数字人ID: {avatar_id}")
        print(f"  📝 对比前台显示的名称")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n🎉 测试完成！")
        else:
            print(f"\n❌ 测试失败！")
    except KeyboardInterrupt:
        print(f"\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
