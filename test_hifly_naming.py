#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试飞影数字人命名逻辑
模拟使用指定视频文件和空命名的情况
"""

import os
import sys
import re
from urllib.parse import urlparse

def extract_name_from_filename(filename):
    """从文件名提取姓名（模拟原始逻辑）"""
    # 移除扩展名
    name_without_ext = os.path.splitext(filename)[0]
    
    # 按照常见分隔符分割
    parts = re.split(r'[-_\s]+', name_without_ext)
    
    # 查找单独的字母（通常是姓名）
    for part in parts:
        if len(part) == 1 and part.isalpha():
            return part
    
    return ""

def extract_name_from_url(video_url):
    """从视频URL中提取文件名并分析命名"""
    try:
        # 解析URL获取路径
        parsed_url = urlparse(video_url)
        path = parsed_url.path
        
        # 获取文件名
        filename = os.path.basename(path)
        
        print(f"📁 从URL提取的文件名: {filename}")
        
        # 移除扩展名
        name_without_ext = os.path.splitext(filename)[0]
        print(f"📝 去除扩展名后: {name_without_ext}")
        
        # 分析文件名结构
        parts = re.split(r'[-_\s]+', name_without_ext)
        print(f"🔍 文件名分割结果: {parts}")
        
        # 查找可能的命名模式
        patterns = []
        
        # 模式1: 查找单独的字母
        single_chars = [part for part in parts if len(part) == 1 and part.isalpha()]
        if single_chars:
            patterns.append(f"单字母: {single_chars}")
        
        # 模式2: 查找中文名字
        chinese_names = [part for part in parts if re.match(r'^[\u4e00-\u9fff]+$', part)]
        if chinese_names:
            patterns.append(f"中文名: {chinese_names}")
        
        # 模式3: 查找数字ID
        numbers = [part for part in parts if part.isdigit()]
        if numbers:
            patterns.append(f"数字ID: {numbers}")
        
        # 模式4: 查找英文名
        english_names = [part for part in parts if re.match(r'^[a-zA-Z]+$', part) and len(part) > 1]
        if english_names:
            patterns.append(f"英文名: {english_names}")
        
        print(f"🎯 识别的命名模式: {patterns}")
        
        return {
            'filename': filename,
            'name_without_ext': name_without_ext,
            'parts': parts,
            'single_chars': single_chars,
            'chinese_names': chinese_names,
            'numbers': numbers,
            'english_names': english_names
        }
        
    except Exception as e:
        print(f"❌ URL解析失败: {e}")
        return None

def test_current_naming_logic():
    """测试当前的命名逻辑"""
    print("=== 测试当前飞影数字人命名逻辑 ===")
    
    # 模拟测试数据
    test_video_path = "D:\\project\\guangliu02\\test\\1.mp4"
    
    print(f"📹 测试视频文件: {test_video_path}")
    
    # 提取文件名
    filename = os.path.basename(test_video_path)
    print(f"📁 文件名: {filename}")
    
    # 使用原始的提取逻辑
    extracted_name = extract_name_from_filename(filename)
    print(f"🎭 提取的演员名称: '{extracted_name}' (空表示未提取到)")
    
    # 模拟不同的ID情况
    test_cases = [
        {"id": "99999", "actor": extracted_name or "未知"},
        {"id": "12345", "actor": extracted_name or "测试"},
        {"id": "38042", "actor": extracted_name or "演员A"},
    ]
    
    print(f"\n🎯 根据当前命名规则生成的数字人名称:")
    for case in test_cases:
        # 当前的命名格式：演员名称-ID-演员
        title = f"{case['actor']}-{case['id']}-演员"
        print(f"  ID={case['id']}, 演员={case['actor']} → 数字人名称: '{title}'")
    
    return extracted_name

def test_url_naming_analysis():
    """测试URL命名分析"""
    print(f"\n=== 测试视频URL命名分析 ===")
    
    # 模拟一些常见的视频URL格式
    test_urls = [
        "https://dv-library.gz.bcebos.com/Files/Uploads/20250729/yangliwei_bj/38050/38050-20.mp4",
        "https://dv-library.gz.bcebos.com/Files/Uploads/20250729/yangliwei_bj/38049/38049-19.mp4",
        "https://example.com/videos/张三-12345-演员.mp4",
        "https://example.com/videos/actor_李四_67890.mp4",
        "https://example.com/videos/1.mp4",  # 对应我们的测试文件
    ]
    
    for url in test_urls:
        print(f"\n📹 分析URL: {url}")
        result = extract_name_from_url(url)
        
        if result:
            # 模拟生成数字人名称
            filename_parts = result['parts']
            
            # 尝试不同的命名策略
            naming_strategies = []
            
            # 策略1: 使用中文名
            if result['chinese_names']:
                for name in result['chinese_names']:
                    for num in result['numbers']:
                        naming_strategies.append(f"{name}-{num}-演员")
            
            # 策略2: 使用英文名
            if result['english_names']:
                for name in result['english_names']:
                    for num in result['numbers']:
                        naming_strategies.append(f"{name}-{num}-演员")
            
            # 策略3: 使用单字母
            if result['single_chars']:
                for char in result['single_chars']:
                    for num in result['numbers']:
                        naming_strategies.append(f"{char}-{num}-演员")
            
            # 策略4: 如果没有明确的名字，使用文件名
            if not naming_strategies and result['numbers']:
                for num in result['numbers']:
                    naming_strategies.append(f"演员-{num}-演员")
            
            print(f"🎯 可能的数字人命名: {naming_strategies}")

def analyze_naming_issue():
    """分析命名问题"""
    print(f"\n=== 分析飞影数字人命名问题 ===")
    
    print(f"🔍 问题描述:")
    print(f"  - 飞影前台页面的数字人名称后面会多几个字母")
    print(f"  - 可能与数字人命名和视频URL有关")
    
    print(f"\n🎯 当前命名规则分析:")
    print(f"  格式: {{演员名称}}-{{ID}}-演员")
    print(f"  示例: 张三-38042-演员")
    
    print(f"\n⚠️ 可能的问题原因:")
    print(f"  1. 演员名称提取不准确")
    print(f"     - 从文件名'1.mp4'无法提取有效的演员名称")
    print(f"     - 可能导致使用默认值或空值")
    print(f"  ")
    print(f"  2. 视频URL中的额外信息")
    print(f"     - URL可能包含额外的标识符")
    print(f"     - 如: 38050-20.mp4 中的'-20'")
    print(f"  ")
    print(f"  3. 飞影API的处理逻辑")
    print(f"     - API可能会自动添加后缀")
    print(f"     - 或者从视频文件名中提取额外信息")
    
    print(f"\n💡 建议的测试方案:")
    print(f"  1. 使用'1.mp4'文件，命名留空")
    print(f"  2. 观察生成的数字人名称")
    print(f"  3. 对比实际命名与预期命名")
    print(f"  4. 分析多出来的字母来源")

def simulate_test_scenario():
    """模拟测试场景"""
    print(f"\n=== 模拟测试场景 ===")
    
    test_file = "D:\\project\\guangliu02\\test\\1.mp4"
    
    print(f"📹 测试文件: {test_file}")
    print(f"🎭 命名设置: 留空")
    
    # 模拟当前的处理逻辑
    filename = os.path.basename(test_file)
    extracted_name = extract_name_from_filename(filename)
    
    print(f"\n🔍 处理过程:")
    print(f"  1. 文件名: {filename}")
    print(f"  2. 提取演员名称: '{extracted_name}' (空表示未提取到)")
    
    # 如果演员名称为空，系统可能的处理方式
    if not extracted_name:
        print(f"  3. 演员名称为空的处理:")
        print(f"     - 可能使用默认值 '演员'")
        print(f"     - 可能使用文件名 '1'")
        print(f"     - 可能使用空字符串 ''")
    
    # 模拟可能的命名结果
    possible_names = []
    
    if extracted_name:
        possible_names.append(f"{extracted_name}-[ID]-演员")
    else:
        possible_names.extend([
            "演员-[ID]-演员",
            "1-[ID]-演员", 
            "-[ID]-演员",
            "[ID]-演员",
            "未知-[ID]-演员"
        ])
    
    print(f"\n🎯 可能的数字人命名结果:")
    for name in possible_names:
        print(f"  - {name}")
    
    print(f"\n⚠️ 需要关注的问题:")
    print(f"  - 如果命名中有空字符串，可能导致格式异常")
    print(f"  - 飞影API可能会自动处理空名称")
    print(f"  - 视频文件本身的元数据可能影响命名")

def main():
    """主测试函数"""
    print("🧪 飞影数字人命名逻辑测试")
    print("=" * 60)
    
    # 测试当前命名逻辑
    extracted_name = test_current_naming_logic()
    
    # 测试URL命名分析
    test_url_naming_analysis()
    
    # 分析命名问题
    analyze_naming_issue()
    
    # 模拟测试场景
    simulate_test_scenario()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    print(f"🎯 关键发现:")
    print(f"  1. 从'1.mp4'无法提取有效的演员名称")
    print(f"  2. 当前命名格式: {{演员名称}}-{{ID}}-演员")
    print(f"  3. 空演员名称可能导致异常命名")
    
    print(f"\n💡 建议的实际测试:")
    print(f"  1. 使用应用程序上传'1.mp4'文件")
    print(f"  2. 命名字段留空")
    print(f"  3. 观察飞影前台显示的数字人名称")
    print(f"  4. 记录多出来的字母内容")
    print(f"  5. 分析字母来源（文件名、URL、API处理等）")
    
    print(f"\n🔧 可能的修复方案:")
    print(f"  - 改进演员名称提取逻辑")
    print(f"  - 添加默认命名处理")
    print(f"  - 清理视频URL中的额外标识符")
    print(f"  - 验证飞影API的命名处理逻辑")

if __name__ == "__main__":
    main()
