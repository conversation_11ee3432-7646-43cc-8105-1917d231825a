#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主窗口类，实现GUI主界面
"""

import os
import sys
import pandas as pd
from datetime import datetime
import config
import asyncio
import json

from PySide6.QtCore import Qt, QSize, QTimer, Slot, Signal, QPropertyAnimation, QEasingCurve, QEvent, QRect, QPoint, QThread
from PySide6.QtGui import QIcon, QFont, QAction, QColor, QPixmap, QPainter, QCursor, QTextCursor, QTextOption, QPen
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QProgressBar, QTableWidget, QTableWidgetItem,
    QHeaderView, QStatusBar, QMessageBox, QFileDialog, QTextEdit,
    QA<PERSON>lication, Q<PERSON>ool<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>rame, QMenu, QDialog,
    QStyledItemDelegate, QAbstractItemView, QToolButton, QSizePolicy,
    QFormLayout, QGroupBox, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QStackedWidget, QStyleOptionViewItem, QStyle, QScrollArea, QProgressDialog,
    QInputDialog
)

# 禁用WebEngine以避免图形渲染问题
WEBENGINE_AVAILABLE = False

# 导入自定义组件
from ui.custom_widgets import ToggleSwitch



print("WebEngine已禁用，使用稳定的备用登录方案")

from core.config_manager import ConfigManager
from core.clipboard_manager import ClipboardManager
from core.processor import AudioProcessor
from core.hifly_upload_manager import HiflyUploadManager
# DigitalHumanManager将在需要时延迟导入
from ui.settings_dialog import SettingsDialog
from ui.schedule_manager import ScheduleManager, ScheduleManagerDialog





# 登录对话框类
class LoginDialog(QDialog):
    """HiFly登录对话框"""

    # 定义信号
    chrome_auth_success = Signal()
    chrome_auth_failed = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("HiFly 登录")
        self.setModal(True)
        self.resize(500, 700)

        # 认证数据
        self.auth_data = None
        self.login_success = False
        self.web_view = None

        # 保存父窗口引用
        self.parent_window = parent

        # 连接信号到槽
        self.chrome_auth_success.connect(self.on_chrome_auth_success)
        self.chrome_auth_failed.connect(self.on_auth_failed_chrome)

        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("HiFly 授权登录")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #333; margin-bottom: 20px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 说明文字
        info_label = QLabel(
            "请选择登录方式：\n\n"
            "🌐 方案1 - 自动登录（推荐）：\n"
            "• 点击'自动登录'按钮\n"
            "• 在弹出的浏览器窗口中输入手机号和验证码\n"
            "• 登录成功后会自动获取认证信息并关闭浏览器\n"
            "• 无需手动点击其他按钮！\n\n"
            "🔗 方案2 - 从已登录浏览器获取：\n"
            "• 在Chrome中打开 https://hifly.cc/video 并登录\n"
            "• 启动Chrome时添加参数：--remote-debugging-port=9222\n"
            "• 点击'从浏览器获取认证信息'"
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            padding: 20px;
            line-height: 1.6;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            color: #495057;
        """)
        layout.addWidget(info_label)

        # 添加一些间距
        layout.addSpacing(20)

        # 按钮布局
        button_layout = QVBoxLayout()

        # 方案1按钮
        self.btn_open_browser = QPushButton("🚀 自动登录")
        self.btn_open_browser.clicked.connect(self.open_browser_login)
        self.btn_open_browser.setStyleSheet("""
            QPushButton {
                padding: 12px 24px;
                font-size: 14px;
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        button_layout.addWidget(self.btn_open_browser)

        button_layout.addSpacing(10)

        # 方案2按钮
        self.btn_get_auth = QPushButton("🌐 Chrome自动认证")
        self.btn_get_auth.clicked.connect(self.get_auth_from_browser)
        self.btn_get_auth.setStyleSheet("""
            QPushButton {
                padding: 12px 24px;
                font-size: 14px;
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1e7e34;
            }
            QPushButton:pressed {
                background-color: #155724;
            }
        """)
        button_layout.addWidget(self.btn_get_auth)

        button_layout.addSpacing(20)

        # 取消按钮
        self.btn_cancel = QPushButton("取消")
        self.btn_cancel.clicked.connect(self.reject)
        self.btn_cancel.setStyleSheet("""
            QPushButton {
                padding: 10px 24px;
                font-size: 14px;
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
            QPushButton:pressed {
                background-color: #3d4142;
            }
        """)
        button_layout.addWidget(self.btn_cancel)

        layout.addLayout(button_layout)



    def open_browser_login(self):
        """使用Playwright打开登录页面"""
        try:
            import asyncio
            import threading
            from PySide6.QtCore import QTimer

            def run_browser_login():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.open_playwright_browser())
                    # 不关闭loop，保持浏览器运行
                except Exception as e:
                    print(f"打开浏览器登录失败: {e}")
                    # 使用QTimer在主线程中显示错误消息
                    QTimer.singleShot(0, lambda: QMessageBox.critical(self, "错误", f"打开浏览器登录失败：{e}"))

            # 在后台线程中运行
            thread = threading.Thread(target=run_browser_login)
            thread.daemon = True
            thread.start()

            # 显示提示信息
            QMessageBox.information(
                self,
                "自动登录启动中",
                "🚀 正在启动自动登录浏览器...\n\n"
                "📝 操作步骤：\n"
                "1. 浏览器将自动打开HiFly登录页面\n"
                "2. 请在浏览器中输入手机号和验证码\n"
                "3. 登录成功后会自动获取认证信息\n"
                "4. 浏览器会自动关闭\n\n"
                "⏰ 最长等待时间：5分钟"
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动浏览器登录失败：{e}")

    async def open_playwright_browser(self):
        """使用Playwright打开浏览器进行登录并自动获取认证信息"""
        try:
            from playwright.async_api import async_playwright

            async with async_playwright() as p:
                # 启动浏览器
                browser = await p.chromium.launch(
                    headless=False,
                    args=[
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        '--disable-blink-features=AutomationControlled',
                        '--no-first-run',
                        '--no-default-browser-check'
                    ]
                )

                # 创建新页面
                page = await browser.new_page()

                # 导航到登录页面
                await page.goto("https://hifly.cc/video")

                print("浏览器已打开，请在浏览器中完成登录")

                # 等待用户登录成功（检测localStorage中的token）
                print("等待用户登录...")

                # 持续检查登录状态，最多等待5分钟
                max_wait_time = 300  # 5分钟
                check_interval = 2   # 每2秒检查一次

                for i in range(0, max_wait_time, check_interval):
                    try:
                        # 检查localStorage中是否有token
                        token = await page.evaluate('''
                            () => {
                                return localStorage.getItem('token');
                            }
                        ''')

                        if token:
                            print(f"✓ 检测到用户已登录，token: {token[:10]}...")

                            # 获取完整的认证信息
                            auth_data = await self.extract_auth_data(page)

                            if auth_data:
                                self.auth_data = auth_data
                                self.login_success = True

                                # 关闭浏览器
                                await browser.close()

                                print("✓ 认证信息获取成功，浏览器已关闭")

                                # 在主线程中显示成功消息
                                from PySide6.QtCore import QTimer
                                QTimer.singleShot(0, lambda: self.on_auto_auth_success())

                                return True

                        # 等待一段时间再检查
                        await page.wait_for_timeout(check_interval * 1000)

                    except Exception as e:
                        print(f"检查登录状态时出错: {e}")
                        await page.wait_for_timeout(check_interval * 1000)
                        continue

                # 超时未登录
                await browser.close()
                print("等待登录超时，浏览器已关闭")

                from PySide6.QtCore import QTimer
                QTimer.singleShot(0, lambda: self.on_login_timeout())

                return False

        except Exception as e:
            print(f"Playwright浏览器打开失败: {e}")
            raise e

    async def extract_auth_data(self, page):
        """从页面提取认证数据"""
        try:
            # 获取localStorage
            local_storage = await page.evaluate('''
                () => {
                    const items = {};
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        items[key] = localStorage.getItem(key);
                    }
                    return items;
                }
            ''')

            # 获取cookies
            cookies = await page.context.cookies(["https://hifly.cc"])

            return {
                "cookies": cookies,
                "localStorage": local_storage
            }

        except Exception as e:
            print(f"提取认证数据失败: {e}")
            return None

    def on_auto_auth_success(self):
        """自动认证成功回调"""
        try:
            # 保存认证数据到文件
            if hasattr(self, 'parent_window') and self.parent_window:
                self.parent_window.save_auth_data(self.auth_data)
                print("✓ 认证数据已保存到文件")

            QMessageBox.information(self, "登录成功", "🎉 登录成功！认证信息已自动获取并保存。")
            self.accept()
        except Exception as e:
            print(f"保存认证数据时出错: {e}")
            QMessageBox.warning(self, "保存失败", f"登录成功但保存认证数据失败：{e}")
            self.accept()

    def on_login_timeout(self):
        """登录超时回调"""
        QMessageBox.warning(
            self,
            "登录超时",
            "⏰ 等待登录超时（5分钟）\n\n"
            "浏览器已自动关闭。\n"
            "如需重新登录，请再次点击'打开登录页面'。"
        )



    def get_auth_from_browser(self):
        """从外部浏览器获取认证信息（方案2：自动打开Chrome并获取）"""
        try:
            import asyncio
            import threading
            from PySide6.QtCore import QTimer

            def run_chrome_auth():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    result = loop.run_until_complete(self.open_chrome_and_get_auth())
                    # 不关闭loop，保持Chrome运行

                    # 使用信号在主线程中更新UI
                    print(f"Chrome认证结果: {result}")
                    if result:
                        self.login_success = True
                        print("准备显示成功提示...")
                        # 发射成功信号
                        self.chrome_auth_success.emit()
                    else:
                        print("认证失败，显示失败提示...")
                        # 发射失败信号
                        self.chrome_auth_failed.emit()
                except Exception as e:
                    print(f"Chrome认证流程出错: {e}")
                    QTimer.singleShot(0, lambda: QMessageBox.critical(self, "错误", f"Chrome认证流程出错：{e}"))

            # 直接启动Chrome认证，不显示提示弹窗
            print("开始Chrome自动认证...")

            # 在后台线程中运行
            thread = threading.Thread(target=run_chrome_auth)
            thread.daemon = True
            thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动Chrome认证失败：{e}")

    def on_auth_success(self):
        """认证成功回调"""
        QMessageBox.information(self, "成功", "认证信息获取成功！")
        self.accept()

    def on_auth_failed(self):
        """认证失败回调"""
        QMessageBox.warning(
            self,
            "获取失败",
            "认证信息获取失败！\n\n"
            "可能的原因：\n"
            "1. Chrome浏览器未启动或未开启远程调试\n"
            "2. 未在 hifly.cc 网站登录\n"
            "3. 网络连接问题\n\n"
            "建议：\n"
            "1. 先点击'打开登录页面'完成登录\n"
            "2. 或手动启动Chrome并添加参数：\n"
            "   --remote-debugging-port=9222"
        )

    def on_chrome_auth_success(self):
        """Chrome认证成功回调"""
        print("执行Chrome认证成功回调...")
        QMessageBox.information(self, "Chrome认证成功", "🎉 Chrome认证成功！认证信息已自动获取并保存。")
        print("成功提示已显示，准备关闭对话框...")
        self.accept()

    def on_auth_failed_chrome(self):
        """Chrome认证失败回调"""
        QMessageBox.warning(
            self,
            "Chrome认证失败",
            "⚠️ Chrome自动认证失败！\n\n"
            "可能的原因：\n"
            "1. Chrome启动失败\n"
            "2. 登录超时（5分钟）\n"
            "3. 网络连接问题\n\n"
            "建议：\n"
            "1. 检查Chrome是否已安装\n"
            "2. 尝试方案1：自动登录\n"
            "3. 手动在Chrome中登录后重试"
        )

    async def open_chrome_and_get_auth(self):
        """打开Chrome浏览器并自动获取认证信息"""
        try:
            import subprocess
            import time
            from playwright.async_api import async_playwright

            # 1. 启动Chrome浏览器（带远程调试端口）
            print("正在启动Chrome浏览器...")

            # Chrome启动命令（使用默认用户数据目录保留缓存和收藏夹）
            chrome_cmd = [
                "chrome.exe",  # 或者使用完整路径
                "--remote-debugging-port=9222",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--no-first-run",
                "--no-default-browser-check",
                # 不指定user-data-dir，使用默认目录保留用户数据
                "https://hifly.cc/video"
            ]

            try:
                # 启动Chrome进程
                chrome_process = subprocess.Popen(
                    chrome_cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )
                print(f"✓ Chrome进程已启动，PID: {chrome_process.pid}")

            except FileNotFoundError:
                # 尝试其他Chrome路径
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', ''))
                ]

                chrome_process = None
                for chrome_path in chrome_paths:
                    if os.path.exists(chrome_path):
                        try:
                            chrome_cmd[0] = chrome_path
                            chrome_process = subprocess.Popen(
                                chrome_cmd,
                                stdout=subprocess.DEVNULL,
                                stderr=subprocess.DEVNULL
                            )
                            print(f"✓ Chrome进程已启动，路径: {chrome_path}")
                            break
                        except Exception as e:
                            print(f"启动Chrome失败 ({chrome_path}): {e}")
                            continue

                if not chrome_process:
                    raise Exception("无法找到或启动Chrome浏览器")

            # 等待Chrome完全启动并开启调试端口
            print("等待Chrome调试端口开启...")
            max_startup_wait = 15  # 最多等待15秒
            for i in range(max_startup_wait):
                try:
                    # 尝试连接调试端口
                    import requests
                    response = requests.get("http://localhost:9222/json", timeout=2)
                    if response.status_code == 200:
                        print(f"✓ Chrome调试端口已开启 (等待了{i+1}秒)")
                        break
                except:
                    pass
                await asyncio.sleep(1)
            else:
                raise Exception("Chrome调试端口启动超时")

            # 2. 使用Playwright连接到Chrome并等待登录
            print("连接到Chrome浏览器...")

            async with async_playwright() as p:
                # 连接到Chrome
                browser = await p.chromium.connect_over_cdp("http://localhost:9222")

                # 获取页面
                contexts = browser.contexts
                if not contexts:
                    # 创建新的上下文
                    context = await browser.new_context()
                else:
                    context = contexts[0]

                pages = context.pages

                # 找到HiFly页面或创建新页面
                hifly_page = None
                for page in pages:
                    try:
                        if "hifly.cc" in page.url:
                            hifly_page = page
                            break
                    except:
                        continue

                if not hifly_page:
                    # 创建新页面并导航到HiFly
                    hifly_page = await context.new_page()
                    await hifly_page.goto("https://hifly.cc/video")
                    print("✓ 已打开HiFly页面")
                else:
                    # 刷新现有页面确保最新状态
                    await hifly_page.reload()
                    print("✓ 已连接到现有HiFly页面")

                # 3. 检查当前登录状态
                print("检查当前登录状态...")

                # 首先检查是否已经登录
                try:
                    token = await hifly_page.evaluate('() => localStorage.getItem("token")')
                    if token:
                        print(f"✓ 用户已登录，token: {token[:10]}...")

                        # 直接获取认证信息
                        auth_data = await self.extract_auth_data(hifly_page)

                        if auth_data:
                            self.auth_data = auth_data

                            # 保存认证数据
                            if hasattr(self, 'parent_window') and self.parent_window:
                                self.parent_window.save_auth_data(self.auth_data)
                                print("✓ 认证数据已保存到文件")

                            print("✓ Chrome认证信息获取成功")

                            # 设置成功标志
                            self.login_success = True
                            return True
                except Exception as e:
                    print(f"检查已有登录状态时出错: {e}")

                # 4. 如果未登录，等待用户登录
                print("用户未登录，等待用户完成登录...")

                max_wait_time = 300  # 5分钟
                check_interval = 3   # 每3秒检查一次

                for i in range(0, max_wait_time, check_interval):
                    try:
                        # 检查localStorage中是否有token
                        token = await hifly_page.evaluate('() => localStorage.getItem("token")')

                        if token:
                            print(f"✓ 检测到用户已登录，token: {token[:10]}...")

                            # 获取完整的认证信息
                            auth_data = await self.extract_auth_data(hifly_page)

                            if auth_data:
                                self.auth_data = auth_data

                                # 保存认证数据
                                if hasattr(self, 'parent_window') and self.parent_window:
                                    self.parent_window.save_auth_data(self.auth_data)
                                    print("✓ 认证数据已保存到文件")

                                print("✓ Chrome认证信息获取成功")

                                # 设置成功标志
                                self.login_success = True
                                return True

                        # 等待一段时间再检查
                        await asyncio.sleep(check_interval)

                    except Exception as e:
                        print(f"检查登录状态时出错: {e}")
                        await asyncio.sleep(check_interval)
                        continue

                # 超时未登录
                print("等待登录超时")
                return False

        except Exception as e:
            print(f"Chrome自动认证失败: {e}")
            return False

    async def get_auth_from_chrome(self):
        """从Chrome浏览器获取认证信息"""
        try:
            from playwright.async_api import async_playwright

            async with async_playwright() as p:
                # 尝试多个端口连接
                connection_urls = [
                    "http://localhost:9222",
                    "http://127.0.0.1:9222",
                    "http://localhost:9223",  # 备用端口
                ]

                browser = None
                for url in connection_urls:
                    try:
                        print(f"尝试连接到: {url}")
                        browser = await p.chromium.connect_over_cdp(url)
                        print(f"✓ 成功连接到: {url}")
                        break
                    except Exception as e:
                        print(f"连接 {url} 失败: {e}")
                        continue

                if not browser:
                    print("所有连接尝试都失败了")
                    return False

                # 获取所有打开的上下文
                contexts = browser.contexts
                if not contexts:
                    print("未找到浏览器上下文")
                    return False

                print(f"找到 {len(contexts)} 个浏览器上下文")

                # 遍历所有上下文寻找HiFly页面
                hifly_page = None
                for context in contexts:
                    pages = context.pages
                    print(f"上下文中有 {len(pages)} 个页面")

                    for page in pages:
                        url = page.url
                        print(f"检查页面: {url}")
                        if "hifly.cc" in url:
                            hifly_page = page
                            print(f"✓ 找到HiFly页面: {url}")
                            break

                    if hifly_page:
                        # 获取cookies
                        cookies = await context.cookies(["https://hifly.cc"])
                        print(f"✓ 获取到 {len(cookies)} 个cookies")

                        # 获取localStorage
                        local_storage = await hifly_page.evaluate('''
                            () => {
                                const items = {};
                                for (let i = 0; i < localStorage.length; i++) {
                                    const key = localStorage.key(i);
                                    items[key] = localStorage.getItem(key);
                                }
                                return items;
                            }
                        ''')
                        print(f"✓ 获取到 {len(local_storage)} 个localStorage项")

                        # 检查是否有token
                        token = local_storage.get("token", "")
                        if token:
                            print(f"✓ 找到用户token: {token[:10]}...")

                            # 保存认证数据
                            self.auth_data = {
                                "cookies": cookies,
                                "localStorage": local_storage
                            }
                            return True
                        else:
                            print("未找到用户token，可能未登录")
                            return False

                print("未找到HiFly网站页面")
                return False

        except Exception as e:
            print(f"获取认证数据失败: {e}")
            return False

# 表格操作代理类
class TableButtonDelegate(QStyledItemDelegate):
    """表格按钮代理类，用于在表格中显示操作按钮"""
    
    # 定义操作信号
    deleteClicked = Signal(int)  # 删除按钮点击信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
    
    def paint(self, painter, option, index):
        # 完全覆盖paint方法，避免调用isPersistentEditorOpen
        # 不再检查编辑器状态，直接在setItemDelegateForColumn后用openPersistentEditor打开所有编辑器
        pass
    
    def createEditor(self, parent, option, index):
        # 创建一个容器小部件来放置按钮
        widget = QWidget(parent)
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)
        
        # 创建删除按钮
        btn_delete = QPushButton("删除")
        btn_delete.setObjectName("deleteButton")
        btn_delete.setToolTip("删除该行")
        btn_delete.setFixedHeight(24)
        btn_delete.setFixedWidth(42)
        btn_delete.setStyleSheet("font-size: 11px; padding: 2px 0; font-family: 'Microsoft YaHei UI', 'Segoe UI', sans-serif;")
        
        # 连接按钮点击信号 - 使用动态获取行号的方式
        def on_delete_clicked():
            # 动态获取当前按钮所在的行号
            current_row = -1
            for row in range(parent.parent().rowCount()):
                if parent.parent().cellWidget(row, index.column()) == widget:
                    current_row = row
                    break
            if current_row >= 0:
                self.deleteClicked.emit(current_row)

        btn_delete.clicked.connect(on_delete_clicked)
        
        # 使用居中对齐
        layout.setAlignment(Qt.AlignCenter)
        
        # 添加按钮到布局
        layout.addWidget(btn_delete)
        
        # 设置布局
        widget.setLayout(layout)
        return widget
    
    def updateEditorGeometry(self, editor, option, index):
        # 更新编辑器几何形状
        editor.setGeometry(option.rect)
        
    def editorEvent(self, event, model, option, index):
        # 不需要特殊处理
        return False

# 添加文本编辑代理类
class TextEditDelegate(QStyledItemDelegate):
    """文本编辑代理，使单元格编辑时文字自动换行"""
    
    def createEditor(self, parent, option, index):
        """创建编辑器"""
        print(f"创建编辑器: 列={index.column()}, 行={index.row()}")
        
        # 使用QTextEdit实现自动换行功能
        editor = QTextEdit(parent)
        
        # 启用自动换行
        editor.setLineWrapMode(QTextEdit.WidgetWidth)
        editor.setWordWrapMode(QTextOption.WrapMode.WordWrap)  # 使用正确的枚举类型
        
        # 禁用滚动条，让整个文本都可见
        editor.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        editor.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # 设置样式表，让编辑框更突出
        editor.setStyleSheet("""
            QTextEdit {
                border: 1px solid #999;
                border-radius: 3px;
                background-color: #fff;
                padding: 5px;
            }
        """)
        
        # 设置文本框为自动换行
        editor.setAcceptRichText(False)  # 不接受富文本，只接受纯文本
        
        # 根据列设置不同的编辑模式
        col = index.column()
        
        # 所有列使用单元格高度作为最小高度，不再设置固定的最小高度
        # 只有在updateEditorGeometry中根据文本内容决定是否扩展
        
        return editor
    
    def setEditorData(self, editor, index):
        """设置编辑器数据"""
        if isinstance(editor, QTextEdit):
            editor.setText(index.data())
            # 移动光标到末尾
            cursor = editor.textCursor()
            cursor.movePosition(QTextCursor.End)
            editor.setTextCursor(cursor)
    
    def setModelData(self, editor, model, index):
        """将编辑器数据更新到模型"""
        if isinstance(editor, QTextEdit):
            model.setData(index, editor.toPlainText())
    
    def sizeHint(self, option, index):
        """提供大小提示，确保单元格有足够高度"""
        size = super().sizeHint(option, index)
        
        # 获取文本内容
        text = index.data()
        if not text:
            size.setHeight(max(size.height(), 60))  # 默认最小高度
            return size
            
        # 计算文本实际需要的高度
        fm = option.fontMetrics
        text_width = option.rect.width() - 10  # 减去边距
        
        # 估算每行能容纳的字符数
        chars_per_line = max(1, int(text_width / fm.averageCharWidth()))
        
        # 估算所需的行数
        lines = text.split('\n')
        estimated_lines = 0
        for line in lines:
            estimated_lines += (len(line) // chars_per_line + 1)
        
        # 计算所需高度，至少3行
        text_height = fm.height() * max(3, estimated_lines) + 20  # 加上边距
        
        # 设置高度
        size.setHeight(max(size.height(), text_height))
        return size
    
    def updateEditorGeometry(self, editor, option, index):
        """更新编辑器几何形状，根据情况选择向上或向下扩展"""
        # 获取单元格位置和大小
        cell_rect = option.rect
        
        # 获取父容器（视图）
        parent = editor.parent()
        viewport = parent.viewport().rect() if hasattr(parent, 'viewport') else parent.rect()
        table = parent
        
        # 获取文本内容
        text = index.data()
        if not text:
            # 如果没有文本，使用原始单元格大小
            editor.setGeometry(cell_rect)
            return
            
        # 使用更精确的方法计算文本所需的高度
        doc = QTextEdit(parent)
        doc.setHtml(text)
        doc.setLineWrapMode(QTextEdit.WidgetWidth)
        doc.setWordWrapMode(QTextOption.WrapMode.WordWrap)
        doc.setFixedWidth(cell_rect.width() - 10)  # 设置固定宽度，减去边距
        doc.document().adjustSize()  # 调整文档大小
        
        # 计算文档实际需要的高度
        doc_height = doc.document().size().height()
        
        # 额外添加空间作为缓冲
        extra_space = 40  # 额外添加40像素的缓冲
        
        # 计算所需的最小高度，如果文本高度小于单元格高度，则使用单元格高度
        required_height = doc_height + extra_space
        
        # 确定是否需要扩展
        need_expand = required_height > cell_rect.height()
        
        if not need_expand:
            # 如果不需要扩展，直接使用单元格的矩形
            editor.setGeometry(cell_rect)
            return
        
        # 检查是否为表格最后几行
        row = index.row()
        total_rows = table.model().rowCount() if hasattr(table, 'model') and table.model() else 0
        
        # 计算行在视窗中的相对位置 (0=顶部, 1=底部)
        row_position_ratio = (cell_rect.top() + cell_rect.height()/2) / viewport.height()
        
        # 如果行在视窗下半部分或是最后几行，更倾向于向上扩展
        is_lower_half = row_position_ratio > 0.4  # 从40%位置开始就倾向向上扩展
        is_last_rows = (total_rows > 0) and (row >= total_rows - 3)  # 最后3行
        
        # 检查单元格下方和上方的可用空间
        space_below = viewport.bottom() - cell_rect.bottom()
        space_above = cell_rect.top() - viewport.top()
        
        # 决定扩展方向
        # 更倾向于向上扩展，当:
        # 1. 单元格在视窗下半部分，或
        # 2. 是表格最后几行，或
        # 3. 下方空间不足且上方有更多空间
        expand_upward = is_lower_half or is_last_rows or (space_below < required_height - cell_rect.height() and space_above > space_below)
        
        # 计算最大可用空间
        available_space = space_above if expand_upward else space_below
        
        if expand_upward:
            # 向上扩展
            editor_rect = QRect(
                cell_rect.left(),
                max(viewport.top(), cell_rect.bottom() - required_height),  # 不超出视口顶部
                cell_rect.width(),
                min(required_height, cell_rect.height() + available_space)  # 不超出视口顶部
            )
        else:
            # 向下扩展
            editor_rect = QRect(
                cell_rect.left(),
                cell_rect.top(),
                cell_rect.width(),
                min(required_height, cell_rect.height() + available_space)  # 不超出视口底部
            )
        
        print(f"单元格尺寸: {cell_rect.width()}x{cell_rect.height()}, "
              f"编辑框尺寸: {editor_rect.width()}x{editor_rect.height()}, "
              f"文本高度: {doc_height}, 向上扩展: {expand_upward}, "
              f"列: {index.column()}, 行: {index.row()}")
        
        # 设置编辑器几何形状
        editor.setGeometry(editor_rect)
    
    def paint(self, painter, option, index):
        """自定义绘制方法，根据列类型使用不同的样式"""
        # 创建一个副本以避免修改原始选项
        myOption = QStyleOptionViewItem(option)
        
        # 取消选择时的虚线框
        myOption.state &= ~QStyle.State_HasFocus
        
        # 设置背景
        if myOption.state & QStyle.State_Selected:
            painter.fillRect(myOption.rect, myOption.palette.highlight())
            painter.setPen(myOption.palette.highlightedText().color())
        else:
            painter.setPen(myOption.palette.text().color())
        
        # 确保文本正确显示
        text = index.data()
        if text:
            # 计算文本绘制区域，添加左侧内边距
            textRect = myOption.rect.adjusted(10, 4, -4, -4)
            
            # 根据列类型使用不同的对齐方式和显示策略
            col = index.column()
            
            if col == 4:  # 克隆文案列
                # 克隆文案列：左对齐，自动换行，过长显示省略号
                self.drawWrappedText(painter, textRect, text, Qt.AlignLeft | Qt.AlignVCenter)
                    
            elif col == 1 or col == 5:  # 命名列和演员形象列
                # 名称列：水平居中对齐并且自动换行
                self.drawWrappedText(painter, textRect, text, Qt.AlignLeft | Qt.AlignVCenter)
            
            else:  # 其他列
                # 其他列也支持自动换行，居中对齐
                self.drawWrappedText(painter, textRect, text, Qt.AlignLeft | Qt.AlignVCenter)
        
        else:
            # 如果没有文本，使用默认绘制
            super().paint(painter, myOption, index)
    
    def drawWrappedText(self, painter, rect, text, alignment):
        """绘制自动换行的文本"""
        fontMetrics = painter.fontMetrics()
        
        # 计算文本能显示多少行
        text_height = fontMetrics.height()
        rect_height = rect.height()
        max_lines = rect_height // text_height
        
        # 分割文本成行
        lines = []
        remaining_text = text
        line_width = rect.width()
        
        # 增加显示的最大行数
        target_lines = min(max_lines, 8)  # 最多显示8行
        
        # 根据宽度拆分文本
        for _ in range(target_lines):
            if not remaining_text:
                break
                
            # 尝试找到适合当前宽度的文本段
            best_fit_length = 0
            for i in range(1, len(remaining_text) + 1):
                test_text = remaining_text[:i]
                if fontMetrics.horizontalAdvance(test_text) <= line_width:
                    best_fit_length = i
                else:
                    break
            
            # 如果找不到适合的长度，至少显示一个字符
            if best_fit_length == 0:
                best_fit_length = 1
                elided_line = fontMetrics.elidedText(remaining_text[:1], Qt.ElideRight, line_width)
                lines.append(elided_line)
            elif best_fit_length < len(remaining_text) and _ == target_lines - 1:
                # 最后一行且文本被截断，使用省略号
                elided_line = fontMetrics.elidedText(remaining_text[:best_fit_length], Qt.ElideRight, line_width)
                lines.append(elided_line)
            else:
                # 完整显示这一部分
                lines.append(remaining_text[:best_fit_length])
            
            # 更新剩余文本
            remaining_text = remaining_text[best_fit_length:]
            if not remaining_text:
                break
        
        # 如果还有剩余文本，在最后一行添加省略号
        if remaining_text and lines:
            lines[-1] = fontMetrics.elidedText(lines[-1], Qt.ElideRight, line_width)
        
        # 决定绘制文本的垂直对齐方式
        is_top_aligned = alignment & Qt.AlignTop
        is_center_aligned = alignment & Qt.AlignVCenter
        is_left_aligned = alignment & Qt.AlignLeft
        
        # 计算垂直起始位置
        y_start = rect.top()
        
        if is_center_aligned:
            # 计算文本总高度
            total_text_height = len(lines) * text_height
            # 计算居中的起始Y坐标
            if total_text_height < rect_height:
                y_start = rect.top() + (rect_height - total_text_height) // 2
        
        # 绘制处理后的文本
        y = y_start
        for line in lines:
            line_rect = QRect(rect.left(), y, rect.width(), text_height)
            
            # 根据水平对齐方式绘制
            if is_left_aligned:
                painter.drawText(line_rect, Qt.AlignLeft | Qt.AlignVCenter, line)
            else:
                painter.drawText(line_rect, Qt.AlignHCenter | Qt.AlignVCenter, line)
                
            y += text_height

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()

        # 创建配置管理器
        self.config_manager = ConfigManager()

        # 创建剪贴板管理器
        self.clipboard_manager = ClipboardManager(QApplication.clipboard())

        # 创建处理器
        self.processor = AudioProcessor(self.config_manager)

        # 创建数字人管理器（延迟导入）
        self._init_digital_human_manager()

        # 创建定时任务管理器
        self.schedule_manager = ScheduleManager()
        self.schedule_manager.log_message.connect(self.append_dh_log)
        self.schedule_manager.task_triggered.connect(self.on_schedule_task_triggered)

        # 创建声音管理器
        self._init_voice_manager()

        # 创建视频素材管理器
        self._init_video_material_manager()

        # 创建飞影上传管理器
        self._init_hifly_upload_manager()

        # 跟踪当前加载的文件路径
        self.current_loaded_file = None

        # 是否正在调整列宽
        self.is_adjusting_columns = False

        # 操作历史记录
        self.history = []
        self.history_index = -1
        self.max_history_size = 20  # 最多保存20个历史状态
        self.is_undoing = False  # 标记是否正在进行撤销操作

        # 连接信号
        self.connect_signals()

        # 设置窗口属性
        self.setup_window()

        # 暗色模式配置
        self.dark_mode = self.config_manager.get("dark_mode", False)
        self.setProperty("darkMode", self.dark_mode)

        # 侧边栏状态
        self.sidebar_collapsed = self.config_manager.get("sidebar_collapsed", False)

        # 创建UI组件
        self.create_widgets()

        # 创建布局
        self.create_layout()

        # 创建状态栏
        self.create_statusbar()

        # 初始化剪贴板监听
        self.init_clipboard_monitoring()

        # 更新主题模式
        self.update_theme_mode()

        # 安装事件过滤器以捕获窗口调整大小事件
        self.installEventFilter(self)

        # 设置键盘事件监听 - 安装事件过滤器代替直接覆盖keyPressEvent
        self.table_scripts.installEventFilter(self)

        # 使用定时器延迟自动加载，确保UI完全初始化
        QTimer.singleShot(100, self.auto_load_daily_tasks)

    def get_icon_path(self, icon_name):
        """获取图标文件的正确路径"""
        # 根据运行环境确定图标路径
        if getattr(sys, 'frozen', False):
            # 打包后的环境
            base_path = os.path.dirname(sys.executable)
            icon_paths = [
                os.path.join(base_path, "_internal", "ui", "icons", icon_name),
                os.path.join(base_path, "ui", "icons", icon_name)
            ]
        else:
            # 开发环境
            icon_paths = [
                os.path.join("src", "ui", "icons", icon_name),
                os.path.join("ui", "icons", icon_name)
            ]

        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                return icon_path

        # 如果都找不到，返回第一个路径（让调用者处理错误）
        return icon_paths[0] if icon_paths else icon_name

    def get_icon(self, icon_name):
        """获取QIcon对象，如果文件不存在则返回空图标"""
        icon_path = self.get_icon_path(icon_name)
        if os.path.exists(icon_path):
            return QIcon(icon_path)
        else:
            print(f"图标文件不存在: {icon_path}")
            return QIcon()  # 返回空图标

    def auto_load_daily_tasks(self):
        """启动时自动加载今日任务数据"""
        try:
            # 自动加载声音克隆的今日任务数据
            self.auto_load_voice_cloning_data()

            # 自动加载数字人的今日任务数据
            self.auto_load_digital_human_data()

        except Exception as e:
            self.append_log(f"自动加载今日任务数据失败: {e}")
            import traceback
            traceback.print_exc()

    def _init_digital_human_manager(self):
        """延迟初始化数字人管理器"""
        try:
            from core.digital_human_manager import DigitalHumanManager

            # 创建实例
            self.digital_human_manager = DigitalHumanManager()

            # 立即连接信号，这样可以看到后续初始化过程中的日志
            self.digital_human_manager.log_message.connect(self.append_dh_log)
            self.digital_human_manager.progress_updated.connect(self.update_dh_progress)
            self.digital_human_manager.upload_completed.connect(self.on_dh_upload_completed)
            self.digital_human_manager.table_updated.connect(self.load_dh_table_data)

            # 检查水印去除器初始化状态
            if self.digital_human_manager.watermark_remover:
                self.append_dh_log("✅ 水印去除器已正确初始化")
            else:
                self.append_dh_log("⚠️ 水印去除器未初始化，尝试重新初始化...")
                # 重新初始化水印去除器
                self.digital_human_manager._init_watermark_remover()

            # 设置配置管理器（这可能会触发重新初始化）
            self.digital_human_manager.set_config_manager(self.config_manager)

            print("✓ 数字人管理器初始化成功")
            self.append_dh_log("✅ 数字人管理器初始化成功")
        except Exception as e:
            error_msg = f"数字人管理器初始化失败: {e}"
            print(f"❌ {error_msg}")
            import traceback
            print(f"错误详情: {traceback.format_exc()}")
            self.digital_human_manager = None

            # 在UI中显示错误
            self.append_dh_log(f"❌ {error_msg}")
            self.append_dh_log("⚠️ 数字人功能将不可用，请检查配置或重启程序")

    def _init_voice_manager(self):
        """初始化声音管理器"""
        try:
            from core.voice_manager import VoiceManager

            # 创建实例
            self.voice_manager = VoiceManager()

            # 连接信号
            self.voice_manager.log_message.connect(self.append_vm_log)
            self.voice_manager.progress_updated.connect(self.update_vm_progress)
            self.voice_manager.fetch_completed.connect(self.on_vm_fetch_completed)
            self.voice_manager.table_updated.connect(self.load_vm_table_data)

            print("✓ 声音管理器初始化成功")
            self.append_vm_log("✅ 声音管理器初始化成功")
        except Exception as e:
            error_msg = f"声音管理器初始化失败: {e}"
            print(f"❌ {error_msg}")
            import traceback
            print(f"错误详情: {traceback.format_exc()}")
            self.voice_manager = None

    def _init_video_material_manager(self):
        """初始化视频素材管理器"""
        try:
            from core.video_material_manager import VideoMaterialManager

            # 创建实例
            self.video_material_manager = VideoMaterialManager(self.config_manager)

            # 连接信号
            self.video_material_manager.log_message.connect(self.append_vm_log)
            self.video_material_manager.progress_updated.connect(self.update_vm_progress)
            self.video_material_manager.update_completed.connect(self.on_material_update_completed)

            print("✓ 视频素材管理器初始化成功")
            self.append_vm_log("✅ 视频素材管理器初始化成功")
        except Exception as e:
            error_msg = f"视频素材管理器初始化失败: {e}"
            print(f"❌ {error_msg}")
            import traceback
            print(f"错误详情: {traceback.format_exc()}")
            self.video_material_manager = None

    def _init_hifly_upload_manager(self):
        """初始化飞影上传管理器"""
        try:
            self.hifly_upload_manager = HiflyUploadManager(self.config_manager)

            # 连接信号
            self.hifly_upload_manager.log_message.connect(self.append_vm_log)
            self.hifly_upload_manager.upload_progress.connect(self.update_hifly_upload_progress)
            self.hifly_upload_manager.upload_completed.connect(self.on_hifly_upload_completed)

            print("✓ 飞影上传管理器初始化成功")

        except Exception as e:
            print(f"✗ 飞影上传管理器初始化失败: {e}")
            self.hifly_upload_manager = None

    def auto_load_voice_cloning_data(self):
        """自动加载声音克隆的今日任务数据"""
        try:
            # 使用处理器的create_daily_task方法，它会正确初始化Excel文件
            work_folders = self.processor.create_daily_task(create_download_folder=False)
            excel_file = work_folders["daily_excel"]

            # 设置处理器的文件路径
            self.processor.current_loaded_file = excel_file
            self.processor.daily_file = excel_file

            # 更新表格显示
            if self.processor.scripts_df is not None:
                self.update_table_from_dataframe(self.processor.scripts_df)
                self.append_log(f"自动加载声音克隆数据: {len(self.processor.scripts_df)} 条记录")
            else:
                self.append_log("自动加载声音克隆数据: 创建了新的空表格")

        except Exception as e:
            self.append_log(f"自动加载声音克隆数据失败: {e}")

    def auto_load_digital_human_data(self):
        """自动加载数字人的今日任务数据"""
        try:
            # 检查数字人管理器是否已初始化
            if not self.digital_human_manager:
                self.append_dh_log("⚠️ 数字人管理器未初始化，跳过数据加载")
                return

            # 检查数字人今日任务表格是否存在
            _, table_path = self.digital_human_manager.ensure_today_folder_and_table()

            if table_path and os.path.exists(table_path):
                # 加载数字人表格数据
                self.load_dh_table_data()
                # 计算表格行数
                row_count = self.dh_table.rowCount()
                self.append_dh_log(f"自动加载数字人数据: {row_count} 条记录")
            else:
                self.append_dh_log("已加载 0 条数字人记录")

        except Exception as e:
            self.append_dh_log(f"自动加载数字人数据失败: {e}")

    def table_key_press_event(self, event):
        """处理表格的键盘事件"""
        # 检测Ctrl+Z组合键
        if event.key() == Qt.Key_Z and event.modifiers() == Qt.ControlModifier:
            self.undo_last_action()
            return
        
        # 调用原始的键盘事件处理函数
        QTableWidget.keyPressEvent(self.table_scripts, event)
    
    def save_current_state(self):
        """保存当前表格状态到历史记录"""
        if self.is_undoing:  # 如果正在撤销，不保存状态
            return
            
        # 获取当前表格数据
        current_state = self.update_processor_data_from_table()
        
        # 如果当前没有数据，则不保存
        if current_state is None or current_state.empty:
            return
            
        # 将当前状态转换为可序列化的格式
        state_data = current_state.to_dict('records')
        
        # 检查是否与最后一个状态相同，避免保存重复状态
        if self.history and self.history_index >= 0:
            last_state = self.history[self.history_index]
            
            # 简单比较两个状态是否相同
            if len(last_state) == len(state_data):
                is_same = True
                for i in range(len(last_state)):
                    if last_state[i] != state_data[i]:
                        is_same = False
                        break
                        
                if is_same:
                    # 状态相同，不保存
                    return
        
        # 如果历史记录已经有内容，检查是否需要删除旧记录
        if self.history_index >= 0 and self.history_index < len(self.history) - 1:
            # 删除当前索引之后的所有历史记录
            self.history = self.history[:self.history_index + 1]
        
        # 添加当前状态到历史记录
        self.history.append(state_data)
        self.history_index = len(self.history) - 1
        
        # 限制历史记录大小
        if len(self.history) > self.max_history_size:
            # 移除最早的历史记录
            self.history = self.history[-self.max_history_size:]
            self.history_index = len(self.history) - 1
            
        print(f"保存历史状态 #{self.history_index+1}/{len(self.history)}，共 {len(current_state)} 行数据")
    
    def undo_last_action(self):
        """撤销上一步操作"""
        # 检查是否有历史记录可以撤销
        if self.history_index <= 0 or not self.history:
            self.statusBar.showMessage("没有可撤销的操作", 2000)
            print("没有可撤销的操作")
            return
            
        # 设置撤销标记，防止在恢复状态时再次保存状态
        self.is_undoing = True
        
        try:
            # 移动到上一个历史记录
            self.history_index -= 1
            
            # 获取要恢复的状态
            state_data = self.history[self.history_index]
            
            # 恢复状态
            restored_df = pd.DataFrame(state_data)
            
            # 更新表格和处理器数据
            self.update_table_from_dataframe(restored_df, append=False)
            self.processor.scripts_df = restored_df
            
            # 更新状态栏
            self.statusBar.showMessage(f"已撤销到历史状态 #{self.history_index+1}/{len(self.history)}", 2000)
            
            print(f"撤销到历史状态 #{self.history_index+1}/{len(self.history)}，共有 {len(restored_df)} 行数据")
            self.append_log(f"已撤销到历史状态 #{self.history_index+1}/{len(self.history)}")
            
            # 自动保存更改
            self.auto_save_to_daily_excel()
        finally:
            # 清除撤销标记
            self.is_undoing = False
    
    def eventFilter(self, watched, event):
        """事件过滤器，处理各种UI事件"""
        # 处理窗口拖动
        if hasattr(self, 'drag_area') and watched == self.drag_area:
            if event.type() == QEvent.MouseButtonPress and event.button() == Qt.LeftButton:
                self.drag_pos = event.globalPos() - self.frameGeometry().topLeft()
                event.accept()
                return True
            elif event.type() == QEvent.MouseMove and event.buttons() & Qt.LeftButton:
                if self.drag_pos is not None:
                    if self.isMaximized():
                        # 如果是最大化状态，先恢复正常
                        self.showNormal()
                        # 调整拖动位置，使鼠标保持在标题栏相对位置
                        drag_pos_ratio = event.pos().x() / watched.width()
                        self.drag_pos = QPoint(int(self.width() * drag_pos_ratio), event.pos().y())
                    self.move(event.globalPos() - self.drag_pos)
                    event.accept()
                    return True
        
        # 处理自定义刷新声音ID事件
        if event.type() == QEvent.Type(QEvent.User + 1):
            self.refresh_voice_id_from_settings()
            return True
        
        # 处理窗口大小改变事件
        if watched == self and event.type() == QEvent.Resize and hasattr(self, 'table_scripts'):
            # 窗口大小调整后，重新调整表格列宽
            QTimer.singleShot(10, self.adjust_column_widths)
            
        # 处理表格的键盘事件
        if hasattr(self, 'table_scripts') and watched == self.table_scripts and event.type() == QEvent.KeyPress:
            # 检测Ctrl+Z组合键
            if event.key() == Qt.Key_Z and event.modifiers() == Qt.ControlModifier:
                self.undo_last_action()
                return True  # 事件已处理
            
            # 调用通用的表格按键处理
            self.table_key_press_event(event)
            return True
                
        # 默认处理
        return super().eventFilter(watched, event)
        
    def refresh_voice_id_from_settings(self):
        """从设置页面发起的声音ID刷新"""
        self.append_log("正在从百度表格刷新声音ID数据...")
        self.append_log("强制刷新模式：跳过缓存，获取最新数据")
        
        # 清除声音ID缓存
        self.processor.clear_cache()
        
        # 清空当前数据，确保重新加载
        self.processor.voice_id_map = {}  
        
        # 使用线程异步刷新，确保设置force_update=True
        self.statusBar.showMessage("正在刷新声音ID数据...")
        self.refresh_voice_id_data(force_update=True)
    
    def adjust_column_widths(self):
        """根据窗口大小调整列宽"""
        if not hasattr(self, 'table_scripts') or self.is_adjusting_columns:
            return
            
        self.is_adjusting_columns = True
        
        # 定义列宽占比
        column_width_ratios = {
            0: 60,    # 匹配人
            1: 100,   # 命名
            2: 80,    # 工单号
            3: 80,    # 副单号
            4: 300,   # 克隆文案
            5: 100,   # 演员形象
            6: 60,    # 素材ID
            7: 70,    # 完成状态
            8: 70,    # 生成时间
            9: 60     # 操作
        }
        
        # 计算比例总和
        total_ratio = sum(column_width_ratios.values())
        
        # 计算表格宽度，减去垂直滚动条宽度
        scrollbar_width = 20  # 估计的滚动条宽度
        table_width = self.table_scripts.width() - scrollbar_width
        
        try:
            # 尝试断开列宽调整信号
            try:
                self.table_scripts.horizontalHeader().sectionResized.disconnect(self.on_section_resized)
            except (TypeError, RuntimeError):
                # 可能信号尚未连接，忽略错误
                pass
            
            # 调整各列宽度
            for col, ratio in column_width_ratios.items():
                if col == 4:  # 克隆文案列
                    continue  # 跳过克隆文案列，它使用Stretch模式
                    
                width = int(table_width * (ratio / total_ratio))
                self.table_scripts.setColumnWidth(col, width)
            
            # 重新连接列宽调整信号
            self.table_scripts.horizontalHeader().sectionResized.connect(self.on_section_resized)
        except Exception as e:
            print(f"调整列宽出错: {e}")
        finally:
            self.is_adjusting_columns = False
    
    def get_expected_columns(self):
        """获取期望的列顺序"""
        return [
            '序号', '命名', '工单号', '副单号', '克隆文案',
            '演员形象', '素材ID', '版型', '完成状态', '生成时间'
        ]

    def filter_dataframe_columns(self, df):
        """过滤DataFrame，只保留必要的列"""
        if df is None or df.empty:
            return df

        # 定义需要保留的列映射（原列名 -> 目标列名）
        column_mapping = {
            'ID': 'ID',                    # 素材ID
            '视频URL': '视频URL',          # 视频链接
            '命名': '命名',
            '工单号': '工单号',
            '副单号': '副单号',
            '克隆文案': '克隆文案',
            '演员形象': '演员形象',
            '素材ID': '素材ID',            # 保留原始列名兼容
            '版型': '版型'
        }

        # 创建新的DataFrame，只包含需要的列
        filtered_data = {}

        # 添加序号列（从1开始）
        filtered_data['序号'] = range(1, len(df) + 1)

        # 复制需要的列
        for original_col, target_col in column_mapping.items():
            if original_col in df.columns:
                filtered_data[target_col] = df[original_col]
            else:
                # 如果列不存在，填充空值
                filtered_data[target_col] = [''] * len(df)

        # 添加状态列
        filtered_data['完成状态'] = ['待处理'] * len(df)
        filtered_data['生成时间'] = [''] * len(df)

        # 创建新的DataFrame
        filtered_df = pd.DataFrame(filtered_data)

        return filtered_df
            
    def setup_table_columns(self):
        """设置表格列"""
        # 设置列数
        self.table_scripts.setColumnCount(11)  # 10个数据列 + 1个操作列
        
        # 设置列标题
        headers = self.get_expected_columns() + ['操作']
        self.table_scripts.setHorizontalHeaderLabels(headers)
        
        # 设置标题行样式
        header = self.table_scripts.horizontalHeader()
        header.setHighlightSections(False)  # 标题行不高亮显示
        header.setStretchLastSection(False)  # 最后一列不自动拉伸
        header.setDefaultAlignment(Qt.AlignCenter | Qt.AlignVCenter)  # 默认居中对齐
        
        # 定义列宽参数
        column_width_data = [
            (0, "序号", 50),      # 序号
            (1, "命名", 90),      # 命名
            (2, "工单号", 80),    # 工单号
            (3, "副单号", 80),    # 副单号
            (4, "克隆文案", 350), # 克隆文案
            (5, "演员形象", 120), # 演员形象
            (6, "素材ID", 70),    # 素材ID
            (7, "版型", 60),      # 版型
            (8, "完成状态", 80),  # 完成状态
            (9, "生成时间", 100), # 生成时间
            (10, "操作", 60)      # 操作
        ]
        
        # 设置列宽和调整模式
        for col, name, width in column_width_data:
            self.table_scripts.setColumnWidth(col, width)
                
            # 只有克隆文案列和演员形象列可以拉伸，其他列固定宽度
            if col == 4:  # 克隆文案列
                header.setSectionResizeMode(col, QHeaderView.Stretch)
            else:
                header.setSectionResizeMode(col, QHeaderView.Interactive)
        
        # 连接窗口大小改变信号，以便调整列宽
        self.table_scripts.horizontalHeader().sectionResized.connect(self.on_section_resized)
    
    def on_section_resized(self, index, oldSize, newSize):
        """列宽度调整后的处理"""
        # 只在用户手动调整列宽时触发
        if self.table_scripts.horizontalHeader().sectionResizeMode(index) == QHeaderView.Interactive:
            self.ensure_uniform_row_height()
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("光流一站式口播助手")
        self.resize(1200, 800)  # 调小默认宽度
        self.setMinimumSize(1000, 700)

        # 允许用户自定义拉伸窗口大小
        # 移除固定大小限制，允许用户调整窗口大小

        # 设置窗口无边框
        self.setWindowFlags(Qt.FramelessWindowHint)

        # 设置窗口图标
        icon_path = self.get_icon_path("guangliu.ico")
        if os.path.exists(icon_path):
            icon = QIcon(icon_path)
            self.setWindowIcon(icon)
            # 强制设置应用程序图标
            QApplication.instance().setWindowIcon(icon)
        else:
            # 尝试备用图标
            icon_path = self.get_icon_path("app.ico")
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
                QApplication.instance().setWindowIcon(icon)

        # 初始化拖动相关变量
        self.drag_pos = None
    
    def connect_signals(self):
        """连接信号到槽函数"""
        # 处理器信号
        self.processor.signals.progress.connect(self.update_progress)
        self.processor.signals.item_status_changed.connect(self.update_item_status)
        self.processor.signals.process_finished.connect(self.on_process_finished)
        self.processor.signals.log_message.connect(self.append_log)
        
        # 剪贴板信号
        self.clipboard_manager.table_data_detected.connect(self.on_clipboard_data_detected)
        
        # 配置管理器信号
        self.config_manager.theme_changed.connect(self.on_theme_changed)
    
    def create_widgets(self):
        """创建UI组件"""
        # 创建自定义标题栏
        self.create_title_bar()

        # 创建侧边栏
        self.create_sidebar()

        # 创建表格视图
        self.create_table_view()

        # 创建工具栏
        self.create_toolbar()

        # 创建底部控制区
        self.create_bottom_controls()

        # 创建日志区域
        self.create_log_area()

        # 创建设置面板（初始隐藏）
        self.create_settings_panel()

        # 当前显示的内容
        self.current_content = "main"  # 'main' 或 'settings'

    def create_title_bar(self):
        """创建自定义标题栏"""
        self.title_bar = QWidget()
        self.title_bar.setObjectName("titleBar")
        self.title_bar.setFixedHeight(48)

        title_layout = QHBoxLayout(self.title_bar)
        title_layout.setContentsMargins(15, 0, 8, 0)  # 左边距从8改为11，增加3像素
        title_layout.setSpacing(8)

        # 程序图标
        self.app_icon = QLabel()
        icon_path = self.get_icon_path("guangliu.ico")
        if os.path.exists(icon_path):
            pixmap = QIcon(icon_path).pixmap(20, 20)
            self.app_icon.setPixmap(pixmap)
        else:
            # 尝试备用图标
            icon_path = self.get_icon_path("app.ico")
            if os.path.exists(icon_path):
                pixmap = QIcon(icon_path).pixmap(20, 20)
                self.app_icon.setPixmap(pixmap)
        self.app_icon.setFixedSize(20, 20)
        title_layout.addWidget(self.app_icon)

        # 程序标题
        self.app_title = QLabel("光流一站式口播助手")
        self.app_title.setObjectName("appTitle")
        title_layout.addWidget(self.app_title)

        # 拖动区域
        self.drag_area = QWidget()
        self.drag_area.setObjectName("dragArea")
        self.drag_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        # 安装事件过滤器以处理拖动
        self.drag_area.installEventFilter(self)

        # 窗口控制按钮
        self.btn_minimize = QPushButton("−")
        self.btn_minimize.setObjectName("windowButton")
        self.btn_minimize.setToolTip("最小化")
        self.btn_minimize.clicked.connect(self.showMinimized)

        self.btn_maximize = QPushButton("□")
        self.btn_maximize.setObjectName("windowButton")
        self.btn_maximize.setToolTip("最大化/还原")
        self.btn_maximize.clicked.connect(self.toggle_maximize)

        self.btn_close = QPushButton("×")
        self.btn_close.setObjectName("windowButton")
        self.btn_close.setProperty("close", True)
        self.btn_close.setToolTip("关闭")
        self.btn_close.clicked.connect(self.close)

        # 添加到布局
        title_layout.addWidget(self.drag_area)
        title_layout.addWidget(self.btn_minimize)
        title_layout.addWidget(self.btn_maximize)
        title_layout.addWidget(self.btn_close)

    def toggle_maximize(self):
        """切换最大化状态"""
        if self.isMaximized():
            self.showNormal()
            self.btn_maximize.setText("□")
        else:
            self.showMaximized()
            self.btn_maximize.setText("❐")

    def create_sidebar(self):
        """创建侧边栏"""
        # 创建侧边栏
        self.sidebar = QWidget()
        self.sidebar.setObjectName("sidebarWidget")
        
        # 创建侧边栏布局
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # 中间功能按钮区域
        icons_area = QWidget()
        icons_area.setObjectName("sidebarIconsArea")
        icons_layout = QVBoxLayout(icons_area)
        icons_layout.setContentsMargins(0, 10, 0, 10)
        icons_layout.setSpacing(10)
        icons_layout.setAlignment(Qt.AlignCenter)

        # 功能图标按钮 - 声音克隆、数字人生成、声音管理和视频管理
        button_icons = [
            ("voice_clone", "声音克隆", self.on_sound_clone_clicked),
            ("digital_human", "数字人生成", self.on_digital_human_clicked),
            ("voice_management", "声音管理", self.on_voice_management_clicked),
            ("video_management", "视频管理", self.on_video_management_clicked),
        ]
        
        for icon_name, tooltip, method in button_icons:
            btn = QPushButton()
            btn.setObjectName("iconButton")
            
            # 设置图标
            icon = self.get_icon(f"{icon_name}.svg")
            btn.setIcon(icon)
            btn.setIconSize(QSize(24, 24))
            btn.setToolTip(tooltip)
            btn.clicked.connect(method)
            
            # 如果是声音克隆按钮，默认选中
            if icon_name == "voice_clone":
                btn.setProperty("selected", True)
                self.btn_sound_clone = btn
                self.current_sidebar_button = btn

            # 保存数字人按钮引用
            if icon_name == "digital_human":
                self.btn_digital_human = btn  # 保存数字人按钮引用

            # 保存声音管理按钮引用
            if icon_name == "voice_management":
                self.btn_voice_management = btn  # 保存声音管理按钮引用

            # 保存视频管理按钮引用
            if icon_name == "video_management":
                self.btn_video_management = btn  # 保存视频管理按钮引用

            icons_layout.addWidget(btn, 0, Qt.AlignCenter)
        
        sidebar_layout.addWidget(icons_area)
        
        # 添加延展空间
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        sidebar_layout.addWidget(spacer)
        
        # 底部设置区域
        bottom_area = QWidget()
        bottom_area.setObjectName("sidebarBottomArea")
        bottom_layout = QVBoxLayout(bottom_area)
        bottom_layout.setContentsMargins(0, 10, 0, 10)
        bottom_layout.setSpacing(10)
        bottom_layout.setAlignment(Qt.AlignCenter)

        # 移除用户登录按钮，根据用户要求

        # 创建设置按钮
        self.btn_settings = QPushButton()
        self.btn_settings.setObjectName("iconButton")
        self.btn_settings.setIcon(self.get_icon("settings_icon.svg"))
        self.btn_settings.setIconSize(QSize(24, 24))
        self.btn_settings.setToolTip("设置")
        self.btn_settings.clicked.connect(self.on_settings_clicked)
        bottom_layout.addWidget(self.btn_settings, 0, Qt.AlignCenter)
        
        sidebar_layout.addWidget(bottom_area)
    
    def create_expanded_sidebar(self):
        """不再使用扩展侧边栏"""
        pass
    
    def create_collapsed_sidebar(self):
        """不再使用折叠侧边栏"""
        pass
    
    def toggle_sidebar(self):
        """侧边栏不再支持切换"""
        pass
    
    def create_toolbar(self):
        """创建工具栏"""
        # 创建工具栏容器
        self.toolbar_container = QWidget()
        self.toolbar_container.setObjectName("toolbarContainer")
        toolbar_layout = QHBoxLayout(self.toolbar_container)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)  # 移除工具栏边距，由搜索框控制对齐
        toolbar_layout.setSpacing(16)
        
        # 添加搜索框
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(8)
        
        # 创建一个搜索框容器
        search_widget = QWidget()
        search_widget.setObjectName("searchWidget")
        search_widget.setFixedWidth(280)
        search_widget.setFixedHeight(36)
        
        # 搜索框内部布局
        search_inner_layout = QHBoxLayout(search_widget)
        search_inner_layout.setContentsMargins(24, 0, 12, 0)  # 设置24px左边距，与表格容器对齐
        search_inner_layout.setSpacing(4)
        
        # 搜索输入框
        self.search_box = QLineEdit()
        self.search_box.setObjectName("searchBox")
        self.search_box.setPlaceholderText("搜索内容...")
        # 启用自动搜索
        self.search_box.textChanged.connect(self.on_search_text_changed)
        self.search_box.returnPressed.connect(self.on_search_clicked)

        # 添加到内部布局
        search_inner_layout.addWidget(self.search_box)

        # 创建清除按钮 - 替代搜索按钮
        clear_button = QPushButton()
        clear_button.setObjectName("searchButton")
        clear_button.setIcon(self.get_icon("clear.svg"))  # 使用X图标表示清除
        clear_button.setIconSize(QSize(16, 16))
        clear_button.setFixedSize(24, 24)
        clear_button.setToolTip("清除搜索")
        clear_button.clicked.connect(self.clear_search)

        # 上一个结果按钮（向左箭头）
        prev_button = QPushButton()
        prev_button.setObjectName("prevSearchButton")
        prev_button.setIcon(self.get_icon("arrow_forward.svg"))  # 向左箭头
        prev_button.setIconSize(QSize(16, 16))
        prev_button.setFixedSize(24, 24)
        prev_button.setToolTip("上一个结果")
        prev_button.clicked.connect(lambda: self.search_table(prev=True))

        # 下一个结果按钮（向右箭头）
        next_button = QPushButton()
        next_button.setObjectName("nextSearchButton")
        next_button.setIcon(self.get_icon("arrow_back.svg"))  # 向右箭头
        next_button.setIconSize(QSize(16, 16))
        next_button.setFixedSize(24, 24)
        next_button.setToolTip("下一个结果")
        next_button.clicked.connect(lambda: self.search_table(next=True))

        # 添加按钮到布局 - 调整顺序：清除、上一个、下一个
        search_inner_layout.addWidget(clear_button)
        search_inner_layout.addWidget(prev_button)
        search_inner_layout.addWidget(next_button)
        
        # 添加到搜索布局
        search_layout.addWidget(search_widget)
        
        # 下一个按钮不再需要，已经放到搜索框内部
        
        # 将搜索布局添加到工具栏
        toolbar_layout.addLayout(search_layout)
        
        toolbar_layout.addStretch()
        
        # 创建今日任务按钮（主要按钮，使用绿色）
        self.btn_daily_task = QPushButton("今日任务")
        self.btn_daily_task.setProperty("class", "primaryButton")  # 使用主要按钮样式
        self.btn_daily_task.setToolTip("创建今日工作环境")
        self.btn_daily_task.setFixedWidth(120)  # 固定宽度，高度由CSS padding控制

        # 创建一个白色图标 - 与其他按钮图标大小一致
        icon = self.get_icon("daily_tasks.svg")
        if not icon.isNull():
            pixmap = icon.pixmap(QSize(16, 16))  # 与其他按钮图标大小一致
            painter = QPainter(pixmap)
            painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
            painter.fillRect(pixmap.rect(), QColor(255, 255, 255))  # 白色
            painter.end()
            white_icon = QIcon(pixmap)
            self.btn_daily_task.setIcon(white_icon)
        self.btn_daily_task.setIconSize(QSize(20, 20))  # 统一为20x20
        self.btn_daily_task.clicked.connect(self.on_daily_task_clicked)

        # 直接添加今日任务按钮，不使用单独容器
        toolbar_layout.addWidget(self.btn_daily_task)

        # 创建加载文案按钮
        self.btn_load_scripts = QPushButton("加载文案")
        self.btn_load_scripts.setObjectName("secondaryButton")
        self.btn_load_scripts.setToolTip("从Excel文件加载文案")
        self.btn_load_scripts.setFixedWidth(120)  # 固定宽度，高度由CSS padding控制
        self.btn_load_scripts.setIcon(self.get_icon("load_script.svg"))
        self.btn_load_scripts.setIconSize(QSize(20, 20))  # 统一为20x20
        self.btn_load_scripts.clicked.connect(self.on_load_scripts_clicked)
        toolbar_layout.addWidget(self.btn_load_scripts)

        # 创建粘贴数据按钮
        self.btn_paste_data = QPushButton("粘贴数据")
        self.btn_paste_data.setObjectName("secondaryButton")
        self.btn_paste_data.setToolTip("从剪贴板粘贴数据")
        self.btn_paste_data.setFixedWidth(120)  # 固定宽度，高度由CSS padding控制
        self.btn_paste_data.setIcon(self.get_icon("paste_data.svg"))
        self.btn_paste_data.setIconSize(QSize(20, 20))  # 统一为20x20
        self.btn_paste_data.clicked.connect(self.on_paste_data_clicked)
        toolbar_layout.addWidget(self.btn_paste_data)

        # 创建刷新数据按钮（次要按钮）
        self.btn_refresh = QPushButton("刷新声音")
        self.btn_refresh.setObjectName("secondaryButton")
        self.btn_refresh.setToolTip("从百度知识库刷新声音ID数据")
        self.btn_refresh.setFixedWidth(120)  # 固定宽度，高度由CSS padding控制
        self.btn_refresh.setIcon(self.get_icon("refresh_voice.svg"))
        self.btn_refresh.setIconSize(QSize(20, 20))  # 统一为20x20
        self.btn_refresh.clicked.connect(self.on_refresh_data_clicked)
        toolbar_layout.addWidget(self.btn_refresh)

        # 创建输出文件夹按钮
        self.btn_open_output = QPushButton("输出位置")
        self.btn_open_output.setObjectName("secondaryButton")
        self.btn_open_output.setToolTip("打开输出文件夹")
        self.btn_open_output.setFixedWidth(120)  # 固定宽度，高度由CSS padding控制
        self.btn_open_output.setIcon(self.get_icon("output_folder.svg"))
        self.btn_open_output.setIconSize(QSize(20, 20))  # 统一为20x20
        self.btn_open_output.clicked.connect(self.on_open_output_clicked)

        # 创建最后一个按钮的容器，添加右边距
        last_button_container = QWidget()
        last_button_layout = QHBoxLayout(last_button_container)
        last_button_layout.setContentsMargins(0, 0, 24, 0)  # 右边距24px
        last_button_layout.addWidget(self.btn_open_output)
        toolbar_layout.addWidget(last_button_container)
    
    def create_table_view(self):
        """创建表格视图"""
        # 创建表格容器框架
        self.table_container = QFrame()
        self.table_container.setObjectName("contentCard")
        table_container_layout = QVBoxLayout(self.table_container)
        table_container_layout.setContentsMargins(0, 0, 0, 0)
        table_container_layout.setSpacing(0)
        
        # 创建表格
        self.table_scripts = QTableWidget()
        self.table_scripts.setObjectName("excelTable")
        self.table_scripts.setSelectionBehavior(QTableWidget.SelectRows)
        self.table_scripts.setSelectionMode(QTableWidget.ExtendedSelection)
        
        # 设置编辑触发器，允许双击编辑、按键编辑等
        self.table_scripts.setEditTriggers(
            QTableWidget.DoubleClicked | 
            QTableWidget.EditKeyPressed | 
            QTableWidget.AnyKeyPressed
        )
        
        # 设置表格外观
        self.table_scripts.verticalHeader().setDefaultSectionSize(36)  # 默认行高
        self.table_scripts.verticalHeader().setVisible(False)          # 隐藏行号
        self.table_scripts.setAlternatingRowColors(False)             # 不使用交替行颜色
        self.table_scripts.setShowGrid(True)                         # 显示网格线
        
        # 设置表头样式
        header = self.table_scripts.horizontalHeader()
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #f0f0f0;
                color: #333333;
                border: none;  /* 移除表头边框 */
                padding: 5px;
                font-weight: bold;
            }
        """)
        header.setDefaultAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # 表头左对齐
        
        # 设置默认行高值
        self.default_row_height = 36
        
        # 设置文本自动换行
        self.table_scripts.setWordWrap(True)
        
        # 设置表格列
        self.setup_table_columns()
        
        # 创建并设置操作列代理
        self.button_delegate = TableButtonDelegate(self.table_scripts)
        self.button_delegate.deleteClicked.connect(self.on_delete_row_clicked)
        self.table_scripts.setItemDelegateForColumn(10, self.button_delegate)  # 操作列索引为10
        
        # 创建表格右键菜单
        self.table_scripts.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table_scripts.customContextMenuRequested.connect(self.show_table_context_menu)
        
        # 连接表格数据变更信号
        self.table_scripts.itemChanged.connect(self.on_table_item_changed)
        
        # 行高变化时确保统一行高
        self.table_scripts.verticalHeader().setSectionResizeMode(QHeaderView.Interactive)
        
        # 设置统一行高事件处理
        self.table_scripts.itemDelegate().closeEditor.connect(self.on_editor_closed)
        
        # 设置文本编辑代理
        self.setup_text_edit_delegate()
        
        # 添加表格到容器
        table_container_layout.addWidget(self.table_scripts)
        
        # 初始化后立即保存一次初始状态
        QTimer.singleShot(500, self.save_current_state)
    
    def setup_text_edit_delegate(self):
        """设置文本编辑代理"""
        # 创建文本编辑代理实例
        text_delegate = TextEditDelegate(self.table_scripts)
        
        # 为除了操作列(索引10)之外的所有列设置文本编辑代理
        for col in range(self.table_scripts.columnCount()):
            if col != 10:  # 跳过操作列
                self.table_scripts.setItemDelegateForColumn(col, text_delegate)
                print(f"为列 {col} 设置文本编辑代理")
        
        return
    
    def create_bottom_controls(self):
        """创建底部控制区"""
        # 创建控制区容器
        self.bottom_controls = QWidget()
        self.bottom_controls.setObjectName("bottomControls")
        controls_layout = QHBoxLayout(self.bottom_controls)
        controls_layout.setContentsMargins(24, 12, 24, 12)  # 设置24px左右边距，与表格容器对齐
        controls_layout.setSpacing(16)
        
        # 创建开始处理按钮
        self.btn_start_process = QPushButton("开始处理")
        self.btn_start_process.setToolTip("开始处理文案")
        self.btn_start_process.setProperty("class", "primaryButton")  # 使用绿色主要按钮样式
        self.btn_start_process.setFixedWidth(120)  # 固定宽度，高度由CSS padding控制

        # 创建白色图标 - 与其他按钮图标大小一致
        icon = self.get_icon("start_process.svg")
        if not icon.isNull():
            pixmap = icon.pixmap(QSize(16, 16))  # 与其他按钮图标大小一致
            painter = QPainter(pixmap)
            painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
            painter.fillRect(pixmap.rect(), QColor(255, 255, 255))  # 白色
            painter.end()
            white_icon = QIcon(pixmap)
            self.btn_start_process.setIcon(white_icon)
        self.btn_start_process.setIconSize(QSize(20, 20))  # 统一为20x20
        self.btn_start_process.clicked.connect(self.on_start_process_clicked)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m - %p%")
        self.progress_bar.setFixedHeight(16)
        
        # 创建状态标签
        self.lbl_status = QLabel("就绪")
        self.lbl_status.setFixedHeight(20)
        
        # 显示进度数值
        self.lbl_progress_count = QLabel("0/100")
        self.lbl_progress_count.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.lbl_progress_count.setFixedWidth(60)
        
        # 添加组件到布局
        controls_layout.addWidget(self.btn_start_process)
        controls_layout.addWidget(self.progress_bar, 1)  # 1是拉伸因子
        controls_layout.addWidget(self.lbl_progress_count)
    
    def create_settings_panel(self):
        """创建设置面板"""
        # 创建设置面板容器
        self.settings_container = QWidget()
        self.settings_container.hide()  # 初始隐藏

        # 创建主布局
        main_settings_layout = QVBoxLayout(self.settings_container)
        main_settings_layout.setContentsMargins(0, 0, 0, 0)
        main_settings_layout.setSpacing(0)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QScrollArea.NoFrame)

        # 创建设置面板的内容容器
        self.settings_panel = QWidget()
        self.settings_panel.setObjectName("settingsPage")

        # 设置滚动区域的内容
        scroll_area.setWidget(self.settings_panel)

        # 添加滚动区域到主布局
        main_settings_layout.addWidget(scroll_area)

        # 创建表单布局
        panel_layout = QVBoxLayout(self.settings_panel)
        panel_layout.setContentsMargins(40, 40, 40, 40)
        panel_layout.setSpacing(20)

        # 创建设置面板标题
        title = QLabel("设置")
        title.setObjectName("pageTitle")
        panel_layout.addWidget(title)

        # 通用设置
        general_group = self.create_general_settings_group()
        panel_layout.addWidget(general_group)

        # 声音克隆设置
        voice_clone_group = self.create_voice_clone_settings_group()
        panel_layout.addWidget(voice_clone_group)

        # 数字人设置
        digital_human_group = self.create_digital_human_settings_group()
        panel_layout.addWidget(digital_human_group)

        # 添加弹性空间
        panel_layout.addStretch()

        # 创建按钮布局
        button_layout = self.create_settings_buttons()
        panel_layout.addLayout(button_layout)

        # 恢复滚动区域
        main_settings_layout.addWidget(scroll_area)

        # 加载当前设置
        self.load_settings_panel()

    def create_general_settings_group(self):
        """创建通用设置组"""
        group = QGroupBox("通用设置")
        group.setObjectName("settingsGroup")
        layout = QVBoxLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(20, 15, 20, 20)

        # 启用剪切板监听
        self.chk_clipboard = ToggleSwitch("启用剪切板监听")
        layout.addWidget(self.chk_clipboard)

        # 启用夜间模式
        self.chk_dark_mode = ToggleSwitch("启用夜间模式")
        layout.addWidget(self.chk_dark_mode)

        return group

    def create_voice_clone_settings_group(self):
        """创建声音克隆设置组"""
        group = QGroupBox("声音克隆设置")
        group.setObjectName("settingsGroup")
        layout = QVBoxLayout(group)
        layout.setSpacing(16)
        layout.setContentsMargins(20, 15, 20, 20)

        # API密钥行
        api_key_layout = QHBoxLayout()
        api_key_label = QLabel("API密钥")
        api_key_label.setObjectName("settingsLabel")
        api_key_label.setFixedWidth(80)
        self.txt_api_key = QLineEdit()
        self.txt_api_key.setObjectName("settingsLineEdit")
        self.txt_api_key.setEchoMode(QLineEdit.Password)
        self.txt_api_key.editingFinished.connect(self.auto_save_settings)  # 添加自动保存
        api_key_layout.addWidget(api_key_label)
        api_key_layout.addWidget(self.txt_api_key)
        layout.addLayout(api_key_layout)

        # API URL行
        url_layout = QHBoxLayout()
        url_label = QLabel("URL")
        url_label.setObjectName("settingsLabel")
        url_label.setFixedWidth(80)
        self.txt_api_endpoint = QLineEdit()
        self.txt_api_endpoint.setObjectName("settingsLineEdit")
        self.txt_api_endpoint.setPlaceholderText("https://api.fish.audio/v1/tts")
        self.txt_api_endpoint.editingFinished.connect(self.auto_save_settings)  # 添加自动保存
        url_layout.addWidget(url_label)
        url_layout.addWidget(self.txt_api_endpoint)
        layout.addLayout(url_layout)

        # 声音模型行
        model_layout = QHBoxLayout()
        model_label = QLabel("声音模型")
        model_label.setObjectName("settingsLabel")
        model_label.setFixedWidth(80)
        self.cmb_model = QComboBox()
        self.cmb_model.setObjectName("settingsComboBox")
        self.cmb_model.addItems(["speech-1.6", "speech-1.5", "speech-1.0", "s1"])
        model_layout.addWidget(model_label)
        model_layout.addWidget(self.cmb_model)
        layout.addLayout(model_layout)

        # 并发数量行
        concurrent_layout = QHBoxLayout()
        concurrent_label = QLabel("并发数量")
        concurrent_label.setObjectName("settingsLabel")
        concurrent_label.setFixedWidth(80)
        self.spn_concurrent = QLineEdit()  # 改为QLineEdit
        self.spn_concurrent.setObjectName("settingsLineEdit")
        self.spn_concurrent.setText("3")
        self.spn_concurrent.setPlaceholderText("1-5")
        concurrent_layout.addWidget(concurrent_label)
        concurrent_layout.addWidget(self.spn_concurrent)
        layout.addLayout(concurrent_layout)

        # 知识库URL行
        kb_url_layout = QHBoxLayout()
        kb_url_label = QLabel("知识库URL")
        kb_url_label.setObjectName("settingsLabel")
        kb_url_label.setFixedWidth(80)
        self.txt_baidu_sheet_url = QLineEdit()
        self.txt_baidu_sheet_url.setObjectName("settingsLineEdit")
        # 添加打开知识库按钮
        self.btn_open_kb = QPushButton("打开知识库")
        self.btn_open_kb.setObjectName("linkButton")
        self.btn_open_kb.setFixedWidth(100)
        self.btn_open_kb.clicked.connect(self.open_baidu_sheet_in_browser)  # 连接点击事件
        kb_url_layout.addWidget(kb_url_label)
        kb_url_layout.addWidget(self.txt_baidu_sheet_url)
        kb_url_layout.addWidget(self.btn_open_kb)
        layout.addLayout(kb_url_layout)

        # 知识库API行
        kb_api_layout = QHBoxLayout()
        kb_api_label = QLabel("知识库API")
        kb_api_label.setObjectName("settingsLabel")
        kb_api_label.setFixedWidth(80)
        self.txt_baidu_sheets_token = QLineEdit()
        self.txt_baidu_sheets_token.setObjectName("settingsLineEdit")
        self.txt_baidu_sheets_token.setEchoMode(QLineEdit.Password)
        kb_api_layout.addWidget(kb_api_label)
        kb_api_layout.addWidget(self.txt_baidu_sheets_token)
        layout.addLayout(kb_api_layout)

        return group

    def create_digital_human_settings_group(self):
        """创建数字人设置组"""
        group = QGroupBox("数字人设置")
        group.setObjectName("settingsGroup")
        layout = QVBoxLayout(group)
        layout.setSpacing(12)
        layout.setContentsMargins(20, 15, 20, 20)

        # 启用无头模式
        self.chk_headless_mode = ToggleSwitch("启用无头模式", checked=True)  # 默认启用
        self.chk_headless_mode.setToolTip("启用后数字人上传将在后台运行，禁用后可以看到浏览器界面便于调试")
        layout.addWidget(self.chk_headless_mode)

        # 视频并发数量设置
        concurrent_layout = QHBoxLayout()
        concurrent_layout.setSpacing(8)

        concurrent_label = QLabel("快速模式批次大小:")
        concurrent_label.setObjectName("settingsLabel")
        concurrent_layout.addWidget(concurrent_label)

        self.spn_video_concurrent = QLineEdit()
        self.spn_video_concurrent.setObjectName("settingsInput")
        self.spn_video_concurrent.setFixedWidth(60)
        self.spn_video_concurrent.setPlaceholderText("6")
        self.spn_video_concurrent.setToolTip("快速模式API批次大小，每批同时提交的任务数量\n所有文件都会处理，只是分批进行\n建议1-10之间")
        concurrent_layout.addWidget(self.spn_video_concurrent)

        concurrent_unit = QLabel("个")
        concurrent_unit.setObjectName("settingsLabel")
        concurrent_layout.addWidget(concurrent_unit)

        concurrent_layout.addStretch()
        layout.addLayout(concurrent_layout)

        # 视频处理方案设置
        video_process_layout = QHBoxLayout()
        video_process_layout.setSpacing(8)

        video_process_label = QLabel("横版视频处理方案:")
        video_process_label.setObjectName("settingsLabel")
        video_process_layout.addWidget(video_process_label)

        self.cmb_video_process_mode = QComboBox()
        self.cmb_video_process_mode.setObjectName("settingsComboBox")
        self.cmb_video_process_mode.addItem("AI水印去除", "watermark_removal")
        self.cmb_video_process_mode.addItem("传统裁剪", "cropping")
        self.cmb_video_process_mode.setToolTip("选择横版视频的处理方案\n竖版视频仍使用裁剪处理")
        video_process_layout.addWidget(self.cmb_video_process_mode)

        video_process_layout.addStretch()
        layout.addLayout(video_process_layout)

        # 羽化参数设置（仅在去水印模式下显示）
        self.feather_group = QGroupBox("羽化参数设置")
        self.feather_group.setObjectName("settingsGroup")
        feather_layout = QVBoxLayout(self.feather_group)

        # 羽化半径
        radius_layout = QHBoxLayout()
        radius_label = QLabel("羽化半径:")
        radius_label.setObjectName("settingsLabel")
        radius_label.setFixedWidth(80)
        self.feather_radius_spin = QSpinBox()
        self.feather_radius_spin.setObjectName("settingsSpinBox")
        self.feather_radius_spin.setRange(1, 20)
        self.feather_radius_spin.setValue(3)  # 默认值，稍后从配置加载
        self.feather_radius_spin.setSuffix(" px")
        self.feather_radius_spin.setToolTip("控制边缘软化程度，值越大边缘越柔和")

        radius_layout.addWidget(radius_label)
        radius_layout.addWidget(self.feather_radius_spin)
        radius_layout.addStretch()

        # 羽化强度
        sigma_layout = QHBoxLayout()
        sigma_label = QLabel("羽化强度:")
        sigma_label.setObjectName("settingsLabel")
        sigma_label.setFixedWidth(80)
        self.feather_sigma_spin = QDoubleSpinBox()
        self.feather_sigma_spin.setObjectName("settingsSpinBox")
        self.feather_sigma_spin.setRange(0.1, 10.0)
        self.feather_sigma_spin.setSingleStep(0.1)
        self.feather_sigma_spin.setValue(1.0)  # 默认值，稍后从配置加载
        self.feather_sigma_spin.setDecimals(1)
        self.feather_sigma_spin.setToolTip("控制羽化的平滑度，值越大越平滑")

        sigma_layout.addWidget(sigma_label)
        sigma_layout.addWidget(self.feather_sigma_spin)
        sigma_layout.addStretch()

        # 边缘模糊半径
        blur_layout = QHBoxLayout()
        blur_label = QLabel("边缘模糊:")
        blur_label.setObjectName("settingsLabel")
        blur_label.setFixedWidth(80)
        self.edge_blur_spin = QSpinBox()
        self.edge_blur_spin.setObjectName("settingsSpinBox")
        self.edge_blur_spin.setRange(1, 50)
        self.edge_blur_spin.setValue(8)  # 默认值，稍后从配置加载
        self.edge_blur_spin.setSuffix(" px")
        self.edge_blur_spin.setToolTip("控制边缘模糊范围，值越大模糊范围越大")

        blur_layout.addWidget(blur_label)
        blur_layout.addWidget(self.edge_blur_spin)
        blur_layout.addStretch()

        feather_layout.addLayout(radius_layout)
        feather_layout.addLayout(sigma_layout)
        feather_layout.addLayout(blur_layout)

        # 重置按钮
        reset_layout = QHBoxLayout()
        reset_button = QPushButton("重置为默认值")
        reset_button.setObjectName("settingsButton")
        reset_button.clicked.connect(self.on_reset_feather_params)
        reset_layout.addStretch()
        reset_layout.addWidget(reset_button)
        feather_layout.addLayout(reset_layout)

        layout.addWidget(self.feather_group)

        # 连接视频处理方案变化事件
        self.cmb_video_process_mode.currentTextChanged.connect(self.update_feather_visibility)

        # 视频处理并发数量
        video_concurrent_layout = QHBoxLayout()
        video_concurrent_layout.setSpacing(8)

        video_concurrent_label = QLabel("视频处理并发数量:")
        video_concurrent_label.setObjectName("settingsLabel")
        video_concurrent_layout.addWidget(video_concurrent_label)

        self.spn_video_processing_concurrent = QSpinBox()
        self.spn_video_processing_concurrent.setObjectName("settingsInput")
        self.spn_video_processing_concurrent.setMinimum(1)
        self.spn_video_processing_concurrent.setMaximum(5)
        self.spn_video_processing_concurrent.setValue(3)
        self.spn_video_processing_concurrent.setToolTip("同时处理的视频数量（水印去除/裁剪）\n数值越大处理速度越快，但会占用更多系统资源\n线程池大小将自动与此值保持一致")
        video_concurrent_layout.addWidget(self.spn_video_processing_concurrent)

        video_concurrent_layout.addStretch()
        layout.addLayout(video_concurrent_layout)

        # 飞影设置分隔线
        hifly_separator_label = QLabel("飞影数字人设置")
        hifly_separator_label.setObjectName("settingsLabel")
        hifly_separator_label.setStyleSheet("font-weight: bold; color: #3b82f6; margin: 10px 0;")
        layout.addWidget(hifly_separator_label)

        # 飞影Token设置
        hifly_token_layout = QHBoxLayout()
        hifly_token_layout.setSpacing(8)

        hifly_token_label = QLabel("飞影API Token:")
        hifly_token_label.setObjectName("settingsLabel")
        hifly_token_label.setFixedWidth(120)
        hifly_token_layout.addWidget(hifly_token_label)

        self.txt_hifly_token = QLineEdit()
        self.txt_hifly_token.setObjectName("settingsInput")
        self.txt_hifly_token.setPlaceholderText("请输入飞影API Token")
        self.txt_hifly_token.setEchoMode(QLineEdit.Password)  # 密码模式，隐藏Token
        self.txt_hifly_token.setToolTip("飞影数字人API Token，可在飞影个人中心->API明细获取\n用于上传视频到飞影数字人平台")
        self.txt_hifly_token.editingFinished.connect(self.auto_save_settings)  # 添加自动保存
        hifly_token_layout.addWidget(self.txt_hifly_token)

        # 显示/隐藏Token按钮
        self.btn_toggle_hifly_token = QPushButton("👁")
        self.btn_toggle_hifly_token.setObjectName("settingsButton")
        self.btn_toggle_hifly_token.setFixedSize(30, 30)
        self.btn_toggle_hifly_token.setToolTip("显示/隐藏Token")
        self.btn_toggle_hifly_token.clicked.connect(self.toggle_hifly_token_visibility)
        hifly_token_layout.addWidget(self.btn_toggle_hifly_token)

        layout.addLayout(hifly_token_layout)

        # 视频下载设置分隔线
        separator_label = QLabel("视频下载设置")
        separator_label.setObjectName("settingsLabel")
        separator_label.setStyleSheet("font-weight: bold; color: #3b82f6; margin: 10px 0;")
        layout.addWidget(separator_label)

        # 并发浏览器数量
        download_concurrent_layout = QHBoxLayout()
        download_concurrent_layout.setSpacing(8)

        download_concurrent_label = QLabel("并发浏览器数:")
        download_concurrent_label.setObjectName("settingsLabel")
        download_concurrent_layout.addWidget(download_concurrent_label)

        self.spn_video_download_concurrent = QSpinBox()
        self.spn_video_download_concurrent.setObjectName("settingsInput")
        self.spn_video_download_concurrent.setMinimum(1)
        self.spn_video_download_concurrent.setMaximum(10)
        self.spn_video_download_concurrent.setValue(3)
        self.spn_video_download_concurrent.setToolTip("视频下载时同时运行的浏览器实例数量")
        download_concurrent_layout.addWidget(self.spn_video_download_concurrent)

        download_concurrent_layout.addStretch()
        layout.addLayout(download_concurrent_layout)

        # 搜索天数
        search_days_layout = QHBoxLayout()
        search_days_layout.setSpacing(8)

        search_days_label = QLabel("搜索天数:")
        search_days_label.setObjectName("settingsLabel")
        search_days_layout.addWidget(search_days_label)

        self.spn_video_search_days = QSpinBox()
        self.spn_video_search_days.setObjectName("settingsInput")
        self.spn_video_search_days.setMinimum(1)
        self.spn_video_search_days.setMaximum(30)
        self.spn_video_search_days.setValue(3)
        self.spn_video_search_days.setToolTip("查找最近几天的创作任务文件夹")
        search_days_layout.addWidget(self.spn_video_search_days)

        search_days_layout.addStretch()
        layout.addLayout(search_days_layout)

        # 启用下载截图
        self.chk_video_enable_screenshots = ToggleSwitch("启用下载截图", checked=False)
        self.chk_video_enable_screenshots.setToolTip("启用后会在下载过程中保存截图，便于调试但会影响性能")
        layout.addWidget(self.chk_video_enable_screenshots)

        return group

    def toggle_hifly_token_visibility(self):
        """切换飞影Token的显示/隐藏状态"""
        if self.txt_hifly_token.echoMode() == QLineEdit.Password:
            self.txt_hifly_token.setEchoMode(QLineEdit.Normal)
            self.btn_toggle_hifly_token.setText("🙈")
        else:
            self.txt_hifly_token.setEchoMode(QLineEdit.Password)
            self.btn_toggle_hifly_token.setText("👁")

    def create_settings_buttons(self):
        """创建设置按钮"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # 取消按钮
        self.btn_cancel_settings = QPushButton("取消")
        self.btn_cancel_settings.setObjectName("secondaryButton")
        self.btn_cancel_settings.clicked.connect(self.cancel_settings)
        button_layout.addWidget(self.btn_cancel_settings)

        # 保存按钮
        self.btn_save_settings = QPushButton("保存设置")
        self.btn_save_settings.setShortcut("Ctrl+S")  # 添加快捷键
        self.btn_save_settings.setToolTip("保存设置 (Ctrl+S)")  # 添加提示
        self.btn_save_settings.clicked.connect(self.save_settings)
        button_layout.addWidget(self.btn_save_settings)

        return button_layout

    def load_settings_panel(self):
        """加载设置到面板"""
        # 通用设置
        self.chk_clipboard.setChecked(self.config_manager.get("enable_clipboard_monitoring", False))
        self.chk_dark_mode.setChecked(self.config_manager.get("dark_mode", False))

        # 声音克隆设置 - Fish Audio API
        self.txt_api_key.setText(self.config_manager.get("api_key", ""))
        self.txt_api_endpoint.setText(self.config_manager.get("api_endpoint", "https://api.fish.audio/v1/tts"))
        model = self.config_manager.get("model", "speech-1.6")
        index = self.cmb_model.findText(model)
        if index >= 0:
            self.cmb_model.setCurrentIndex(index)

        # 声音克隆设置 - 声音ID设置
        self.txt_baidu_sheet_url.setText(self.config_manager.get("baidu_sheet_url", ""))
        self.txt_baidu_sheets_token.setText(self.config_manager.get("baidu_sheets_token", ""))

        # 声音克隆设置 - 处理设置
        self.spn_concurrent.setText(str(self.config_manager.get("max_concurrent", 3)))

        # 数字人设置
        self.chk_headless_mode.setChecked(self.config_manager.get("headless_mode", True))

        # 飞影设置
        self.txt_hifly_token.setText(self.config_manager.get("hifly_token", ""))

        # 视频处理方案
        video_process_mode = self.config_manager.get("video_processing_mode", "watermark_removal")
        index = self.cmb_video_process_mode.findData(video_process_mode)
        if index >= 0:
            self.cmb_video_process_mode.setCurrentIndex(index)

        # 视频处理并发数量
        self.spn_video_processing_concurrent.setValue(self.config_manager.get("video_processing_concurrent", 3))

        # API批次大小
        self.spn_video_concurrent.setText(str(self.config_manager.get("api_batch_size", 6)))

        # 视频下载设置
        self.spn_video_download_concurrent.setValue(self.config_manager.get("video_download_concurrent_browsers", 3))
        self.spn_video_search_days.setValue(self.config_manager.get("video_download_search_days", 3))
        self.chk_video_enable_screenshots.setChecked(self.config_manager.get("video_download_enable_screenshots", False))

        # 羽化参数
        self.feather_radius_spin.setValue(self.config_manager.get("feather_radius", 3))
        self.feather_sigma_spin.setValue(self.config_manager.get("feather_sigma", 1.0))
        self.edge_blur_spin.setValue(self.config_manager.get("edge_blur_radius", 8))

        # 根据当前模式显示/隐藏羽化设置
        self.update_feather_visibility()
    
    def create_layout(self):
        """创建布局"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局（垂直布局，包含标题栏和内容区）
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 添加标题栏
        main_layout.addWidget(self.title_bar)

        # 创建内容区域（水平布局，包含侧边栏和右侧内容）
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # 添加侧边栏
        content_layout.addWidget(self.sidebar)

        # 创建右侧内容部分 - 整个右侧区域作为内容区域
        right_container = QWidget()
        right_container.setObjectName("contentArea")  # 设置为内容区域样式
        right_layout = QVBoxLayout(right_container)
        right_layout.setContentsMargins(24, 24, 24, 24)  # 增加内边距
        right_layout.setSpacing(0)

        # 创建内容栈
        self.content_stack = QStackedWidget()

        # 创建主内容容器
        main_content = QWidget()
        main_content_layout = QVBoxLayout(main_content)
        main_content_layout.setContentsMargins(0, 0, 0, 0)
        main_content_layout.setSpacing(24)  # 增加组件间距

        # 添加工具栏
        main_content_layout.addWidget(self.toolbar_container)

        # 直接添加表格容器（不使用分割器），设置拉伸因子让表格占用更多空间
        main_content_layout.addWidget(self.table_container, 1)  # 拉伸因子为1，占用主要空间

        # 添加开始处理模块（不设置拉伸因子，保持固定高度）
        main_content_layout.addWidget(self.bottom_controls)

        # 添加日志区域（不设置拉伸因子，保持固定高度）
        main_content_layout.addWidget(self.log_container)
        
        # 创建数字人页面
        self.digital_human_page = self.create_digital_human_page()

        # 创建声音管理页面
        self.voice_management_page = self.create_voice_management_page()

        # 创建视频管理页面
        self.video_management_page = self.create_video_management_page()

        # 添加主内容、数字人页面、声音管理页面、视频管理页面和设置面板到栈
        self.content_stack.addWidget(main_content)
        self.content_stack.addWidget(self.digital_human_page)
        self.content_stack.addWidget(self.voice_management_page)
        self.content_stack.addWidget(self.video_management_page)
        self.content_stack.addWidget(self.settings_container)
        
        # 添加内容栈到右侧布局
        right_layout.addWidget(self.content_stack)

        # 添加右侧容器到内容布局
        content_layout.addWidget(right_container)

        # 将内容布局添加到主布局
        content_widget = QWidget()
        content_widget.setLayout(content_layout)
        main_layout.addWidget(content_widget)

        # 设置默认选中状态
        self.update_sidebar_button_states("sound_clone")

    def create_digital_human_page(self):
        """创建数字人页面"""
        page = QWidget()
        page_layout = QVBoxLayout(page)
        page_layout.setContentsMargins(0, 0, 0, 0)
        page_layout.setSpacing(24)

        # 创建工具栏
        toolbar_container = self.create_digital_human_toolbar()
        page_layout.addWidget(toolbar_container)

        # 创建表格容器
        table_container = self.create_digital_human_table()
        page_layout.addWidget(table_container, 1)  # 拉伸因子为1

        # 创建底部控制区
        bottom_controls = self.create_digital_human_bottom_controls()
        page_layout.addWidget(bottom_controls)

        # 创建日志区域
        log_container = self.create_digital_human_log()
        page_layout.addWidget(log_container)

        return page

    def create_voice_management_page(self):
        """创建声音管理页面"""
        page = QWidget()
        page_layout = QVBoxLayout(page)
        page_layout.setContentsMargins(0, 0, 0, 0)
        page_layout.setSpacing(24)

        # 创建工具栏
        toolbar_container = self.create_voice_management_toolbar()
        page_layout.addWidget(toolbar_container)

        # 创建表格容器
        table_container = self.create_voice_management_table()
        page_layout.addWidget(table_container, 1)  # 拉伸因子为1

        # 创建底部控制区
        bottom_controls = self.create_voice_management_bottom_controls()
        page_layout.addWidget(bottom_controls)

        # 创建日志区域
        log_container = self.create_voice_management_log()
        page_layout.addWidget(log_container)

        return page

    def create_video_management_page(self):
        """创建视频管理页面"""
        page = QWidget()
        page_layout = QVBoxLayout(page)
        page_layout.setContentsMargins(0, 0, 0, 0)
        page_layout.setSpacing(24)

        # 创建工具栏
        toolbar_container = self.create_video_management_toolbar()
        page_layout.addWidget(toolbar_container)

        # 创建表格容器
        table_container = self.create_video_management_table()
        page_layout.addWidget(table_container, 1)  # 拉伸因子为1

        # 创建底部控制区
        bottom_controls = self.create_video_management_bottom_controls()
        page_layout.addWidget(bottom_controls)

        # 创建日志区域
        log_container = self.create_video_management_log()
        page_layout.addWidget(log_container)

        return page

    def create_video_management_toolbar(self):
        """创建视频管理工具栏"""
        toolbar_container = QWidget()
        toolbar_container.setObjectName("toolbarContainer")
        toolbar_layout = QHBoxLayout(toolbar_container)
        toolbar_layout.setContentsMargins(0, 8, 16, 8)
        toolbar_layout.setSpacing(16)

        # 创建搜索框容器
        search_container = QWidget()
        search_container.setObjectName("searchWidget")
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(24, 0, 12, 0)
        search_layout.setSpacing(8)

        # 搜索输入框
        self.vm_search_box = QLineEdit()
        self.vm_search_box.setObjectName("searchBox")
        self.vm_search_box.setPlaceholderText("搜索视频素材...")
        # 启用自动搜索
        self.vm_search_box.textChanged.connect(self.on_vm_search_text_changed)
        self.vm_search_box.returnPressed.connect(self.on_vm_search_clicked)
        search_layout.addWidget(self.vm_search_box)

        # 创建清除按钮
        clear_button = QPushButton()
        clear_button.setObjectName("searchButton")
        clear_button.setIcon(self.get_icon("clear.svg"))
        clear_button.setIconSize(QSize(16, 16))
        clear_button.setFixedSize(24, 24)
        clear_button.setToolTip("清除搜索")
        clear_button.clicked.connect(self.clear_vm_search)
        search_layout.addWidget(clear_button)

        # 上一个结果按钮（向左箭头）
        prev_button = QPushButton()
        prev_button.setObjectName("prevSearchButton")
        prev_button.setIcon(self.get_icon("arrow_forward.svg"))  # 向左箭头
        prev_button.setIconSize(QSize(16, 16))
        prev_button.setFixedSize(24, 24)
        prev_button.setToolTip("上一个结果")
        prev_button.clicked.connect(lambda: self.vm_search_table(prev=True))
        search_layout.addWidget(prev_button)

        # 下一个结果按钮（向右箭头）
        next_button = QPushButton()
        next_button.setObjectName("nextSearchButton")
        next_button.setIcon(self.get_icon("arrow_back.svg"))  # 向右箭头
        next_button.setIconSize(QSize(16, 16))
        next_button.setFixedSize(24, 24)
        next_button.setToolTip("下一个结果")
        next_button.clicked.connect(lambda: self.vm_search_table(next=True))
        search_layout.addWidget(next_button)

        toolbar_layout.addWidget(search_container)
        toolbar_layout.addStretch()

        # 创建素材更新按钮
        self.btn_material_update = QPushButton("素材更新")
        self.btn_material_update.setObjectName("secondaryButton")
        self.btn_material_update.setToolTip("从网站下载最新素材表格并更新本地数据")
        self.btn_material_update.setFixedWidth(120)
        self.btn_material_update.setIcon(self.get_icon("refresh_voice.svg"))
        self.btn_material_update.setIconSize(QSize(20, 20))
        self.btn_material_update.clicked.connect(self.on_material_update_clicked)
        toolbar_layout.addWidget(self.btn_material_update)

        # 创建素材位置按钮
        self.btn_material_location = QPushButton("素材位置")
        self.btn_material_location.setObjectName("secondaryButton")
        self.btn_material_location.setToolTip("打开素材文件夹")
        self.btn_material_location.setFixedWidth(120)
        self.btn_material_location.setIcon(self.get_icon("output_folder.svg"))
        self.btn_material_location.setIconSize(QSize(20, 20))
        self.btn_material_location.clicked.connect(self.on_material_location_clicked)
        toolbar_layout.addWidget(self.btn_material_location)



        return toolbar_container

    def create_video_management_table(self):
        """创建视频管理表格"""
        table_container = QWidget()
        table_container.setObjectName("tableContainer")
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(0, 0, 0, 0)
        table_layout.setSpacing(0)

        # 创建表格
        self.vm_table = QTableWidget()
        self.vm_table.setObjectName("dataTable")

        # 设置表格样式 - 添加外边框
        self.vm_table.setStyleSheet("""
            QTableWidget#dataTable {
                border: 1px solid #e5e7eb;
                border-radius: 12px;
                background-color: #ffffff;
                gridline-color: #f3f4f6;
                selection-background-color: #eff6ff;
            }
            QTableWidget#dataTable::item {
                padding: 8px;
                border-bottom: 1px solid #f3f4f6;
            }
            QTableWidget#dataTable::item:selected {
                background-color: #eff6ff;
                color: #1e40af;
            }
            QHeaderView::section {
                background-color: #f8fafc;
                padding: 12px 8px;
                border: none;
                border-bottom: 2px solid #e5e7eb;
                font-weight: 600;
                color: #374151;
            }
        """)

        # 设置列标题
        headers = ["序号", "ID", "视频URL", "上传人邮箱后缀", "拍摄演员名称", "视频版型", "场景", "表现形式", "服装", "是否上传飞影", "更新日期"]
        self.vm_table.setColumnCount(len(headers))
        self.vm_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.vm_table.setAlternatingRowColors(True)
        self.vm_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.vm_table.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.vm_table.setSortingEnabled(True)
        self.vm_table.verticalHeader().setVisible(False)

        # 设置列宽
        header = self.vm_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 序号
        header.resizeSection(0, 60)
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # ID
        header.resizeSection(1, 100)
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # 视频URL
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # 上传人邮箱后缀
        header.resizeSection(3, 120)
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # 拍摄演员名称
        header.resizeSection(4, 120)
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # 视频版型
        header.resizeSection(5, 80)
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # 场景
        header.resizeSection(6, 100)
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # 表现形式
        header.resizeSection(7, 100)
        header.setSectionResizeMode(8, QHeaderView.Fixed)  # 服装
        header.resizeSection(8, 80)
        header.setSectionResizeMode(9, QHeaderView.Fixed)  # 是否上传飞影
        header.resizeSection(9, 100)
        header.setSectionResizeMode(10, QHeaderView.Fixed)  # 更新日期
        header.resizeSection(10, 120)

        table_layout.addWidget(self.vm_table)
        return table_container

    def create_video_management_bottom_controls(self):
        """创建视频管理底部控制区"""
        bottom_controls = QWidget()
        bottom_controls.setObjectName("bottomControls")
        controls_layout = QHBoxLayout(bottom_controls)
        controls_layout.setContentsMargins(24, 12, 24, 12)
        controls_layout.setSpacing(16)

        # 创建飞影上传按钮
        self.btn_feying_upload = QPushButton("飞影上传")
        self.btn_feying_upload.setToolTip("上传标记为未上传的素材到飞影平台")
        self.btn_feying_upload.setProperty("class", "primaryButton")
        self.btn_feying_upload.setFixedWidth(120)
        self.btn_feying_upload.clicked.connect(self.on_feying_upload_clicked)
        controls_layout.addWidget(self.btn_feying_upload)

        # 添加进度条（初始隐藏）
        self.vm_progress_bar = QProgressBar()
        self.vm_progress_bar.setObjectName("progressBar")
        self.vm_progress_bar.setFixedSize(200, 24)
        self.vm_progress_bar.setVisible(False)
        self.vm_progress_bar.setFormat("上传进度: %v/%m")
        controls_layout.addWidget(self.vm_progress_bar)

        # 添加弹性空间
        controls_layout.addStretch()

        return bottom_controls

    def create_video_management_log(self):
        """创建视频管理日志区域"""
        log_container = QWidget()
        log_container.setObjectName("logContainer")
        log_layout = QVBoxLayout(log_container)
        log_layout.setContentsMargins(0, 0, 0, 0)
        log_layout.setSpacing(8)

        # 日志标题和展开/收起按钮
        log_header = QWidget()
        log_header_layout = QHBoxLayout(log_header)
        log_header_layout.setContentsMargins(0, 0, 0, 0)
        log_header_layout.setSpacing(8)

        log_title = QLabel("操作日志")
        log_title.setObjectName("logTitle")
        log_header_layout.addWidget(log_title)

        # 展开/收起按钮
        self.vm_log_toggle_btn = QPushButton("收起")
        self.vm_log_toggle_btn.setObjectName("toggleButton")
        self.vm_log_toggle_btn.setFixedSize(60, 24)
        self.vm_log_toggle_btn.clicked.connect(self.toggle_vm_log)
        log_header_layout.addWidget(self.vm_log_toggle_btn)

        log_header_layout.addStretch()
        log_layout.addWidget(log_header)

        # 日志文本区域
        self.vm_log_text = QTextEdit()
        self.vm_log_text.setObjectName("logText")
        self.vm_log_text.setMaximumHeight(200)
        self.vm_log_text.setReadOnly(True)

        # 设置日志区域样式 - 添加边框
        self.vm_log_text.setStyleSheet("""
            QTextEdit#logText {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                background-color: #ffffff;
                padding: 8px;
                font-family: "Consolas", "Monaco", "Courier New", monospace;
                font-size: 12px;
                line-height: 1.4;
            }
        """)

        log_layout.addWidget(self.vm_log_text)

        return log_container

    def create_digital_human_toolbar(self):
        """创建数字人工具栏"""
        toolbar_container = QWidget()
        toolbar_container.setObjectName("toolbarContainer")
        toolbar_layout = QHBoxLayout(toolbar_container)
        toolbar_layout.setContentsMargins(0, 8, 16, 8)  # 减小边距，降低高度
        toolbar_layout.setSpacing(16)

        # 创建搜索框容器
        search_container = QWidget()
        search_container.setObjectName("searchWidget")
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(24, 0, 12, 0)  # 左侧增加，右侧减少
        search_layout.setSpacing(8)

        # 搜索输入框
        self.dh_search_box = QLineEdit()
        self.dh_search_box.setObjectName("searchBox")
        self.dh_search_box.setPlaceholderText("搜索数字人记录...")
        search_layout.addWidget(self.dh_search_box)

        # 搜索按钮
        self.dh_search_btn = QPushButton()
        self.dh_search_btn.setObjectName("searchButton")
        self.dh_search_btn.setIcon(self.get_icon("search_icon.svg"))
        self.dh_search_btn.setToolTip("搜索")
        self.dh_search_btn.clicked.connect(self.on_dh_search_clicked)
        search_layout.addWidget(self.dh_search_btn)

        # 上一个/下一个按钮
        self.dh_prev_btn = QPushButton()
        self.dh_prev_btn.setObjectName("prevSearchButton")
        self.dh_prev_btn.setIcon(self.get_icon("arrow_forward.svg"))
        self.dh_prev_btn.setToolTip("上一个")
        search_layout.addWidget(self.dh_prev_btn)

        self.dh_next_btn = QPushButton()
        self.dh_next_btn.setObjectName("nextSearchButton")
        self.dh_next_btn.setIcon(self.get_icon("arrow_back.svg"))
        self.dh_next_btn.setToolTip("下一个")
        search_layout.addWidget(self.dh_next_btn)

        toolbar_layout.addWidget(search_container)
        toolbar_layout.addStretch()  # 添加弹性空间

        # 创建今日任务按钮
        self.btn_load_audio = QPushButton("今日任务")
        self.btn_load_audio.setObjectName("secondaryButton")
        self.btn_load_audio.setToolTip("刷新今日任务数据")
        self.btn_load_audio.setFixedWidth(120)
        self.btn_load_audio.setIcon(self.get_icon("daily_tasks.svg"))
        self.btn_load_audio.setIconSize(QSize(20, 20))
        self.btn_load_audio.clicked.connect(self.on_load_audio_clicked)
        toolbar_layout.addWidget(self.btn_load_audio)

        # 创建音频位置按钮
        self.btn_dh_output = QPushButton("音频位置")
        self.btn_dh_output.setObjectName("secondaryButton")
        self.btn_dh_output.setToolTip("设置音频文件位置")
        self.btn_dh_output.setFixedWidth(120)
        self.btn_dh_output.setIcon(self.get_icon("output_folder.svg"))
        self.btn_dh_output.setIconSize(QSize(20, 20))
        self.btn_dh_output.clicked.connect(self.on_dh_output_clicked)
        toolbar_layout.addWidget(self.btn_dh_output)

        # 创建视频下载按钮
        self.btn_video_download = QPushButton("视频下载")
        self.btn_video_download.setObjectName("primaryButton")
        self.btn_video_download.setToolTip("下载最近3天完成的积分模式和暗黑模式视频")
        self.btn_video_download.setFixedWidth(120)
        self.btn_video_download.setIcon(self.get_icon("download.svg"))
        self.btn_video_download.setIconSize(QSize(20, 20))
        self.btn_video_download.clicked.connect(self.on_video_download_clicked)
        toolbar_layout.addWidget(self.btn_video_download)

        # 创建更新授权按钮
        self.btn_update_auth = QPushButton("更新授权")
        self.btn_update_auth.setObjectName("secondaryButton")
        self.btn_update_auth.setToolTip("更新HiFly平台授权信息")
        self.btn_update_auth.setFixedWidth(120)
        self.btn_update_auth.setIcon(self.get_icon("refresh_voice.svg"))
        self.btn_update_auth.setIconSize(QSize(20, 20))
        self.btn_update_auth.clicked.connect(self.on_update_auth_clicked)
        toolbar_layout.addWidget(self.btn_update_auth)



        return toolbar_container

    def create_digital_human_table(self):
        """创建数字人表格"""
        table_container = QWidget()
        table_container.setObjectName("tableContainer")
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(24, 16, 24, 16)
        table_layout.setSpacing(0)

        # 创建表格
        self.dh_table = QTableWidget()
        self.dh_table.setObjectName("dataTable")

        # 设置表格列
        headers = ["序号", "名称", "素材ID", "生成模式", "上传情况", "上传时间", "文件夹", "工单号", "任务ID", "版型", "完成状态", "完成日期", "删除"]
        self.dh_table.setColumnCount(len(headers))
        self.dh_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.dh_table.setAlternatingRowColors(True)
        self.dh_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.dh_table.setSelectionMode(QAbstractItemView.ExtendedSelection)  # 允许多选
        self.dh_table.setSortingEnabled(True)
        self.dh_table.setShowGrid(True)  # 显示网格线

        # 添加右键菜单
        self.dh_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.dh_table.customContextMenuRequested.connect(self.show_dh_context_menu)

        # 设置表格边框样式
        self.dh_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
                gridline-color: #f0f0f0;
            }
            QTableWidget::item {
                border: none;
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                border: none;
                border-bottom: 1px solid #e0e0e0;
                padding: 8px;
                font-weight: bold;
            }
        """)

        # 设置列宽 - 等比例缩放
        header = self.dh_table.horizontalHeader()

        # 设置所有列为等比例缩放模式
        header.setSectionResizeMode(QHeaderView.Stretch)

        # 设置初始列宽比例（通过设置最小宽度来控制相对大小）
        header.setMinimumSectionSize(50)  # 设置最小列宽

        # 通过设置初始宽度来控制列的相对比例
        self.dh_table.setColumnWidth(0, 60)   # 序号 - 较窄
        self.dh_table.setColumnWidth(1, 200)  # 名称 - 中等
        self.dh_table.setColumnWidth(2, 80)   # 素材ID - 较窄
        self.dh_table.setColumnWidth(3, 80)   # 生成模式 - 较窄
        self.dh_table.setColumnWidth(4, 80)   # 上传情况 - 较窄
        self.dh_table.setColumnWidth(5, 160)  # 上传时间 - 加宽
        self.dh_table.setColumnWidth(6, 80)   # 文件夹 - 较窄
        self.dh_table.setColumnWidth(7, 80)   # 工单号 - 较窄
        self.dh_table.setColumnWidth(8, 100)  # 完成状态 - 缩窄
        self.dh_table.setColumnWidth(9, 100)  # 完成日期 - 中等

        # 设置行高
        self.dh_table.verticalHeader().setDefaultSectionSize(40)
        self.dh_table.verticalHeader().setVisible(False)

        table_layout.addWidget(self.dh_table)

        return table_container

    def create_digital_human_bottom_controls(self):
        """创建数字人底部控制区"""
        bottom_controls = QWidget()
        bottom_controls.setObjectName("bottomControls")
        controls_layout = QHBoxLayout(bottom_controls)
        controls_layout.setContentsMargins(24, 12, 24, 12)
        controls_layout.setSpacing(16)

        # 创建开始上传按钮
        self.btn_start_upload = QPushButton("开始上传")
        self.btn_start_upload.setToolTip("开始上传音频到HiFly平台")
        self.btn_start_upload.setProperty("class", "primaryButton")
        self.btn_start_upload.setFixedWidth(120)

        # 创建白色图标
        icon = self.get_icon("start_process.svg")
        if not icon.isNull():
            pixmap = icon.pixmap(QSize(20, 20))
            painter = QPainter(pixmap)
            painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
            painter.fillRect(pixmap.rect(), QColor(255, 255, 255))
            painter.end()
            white_icon = QIcon(pixmap)
            self.btn_start_upload.setIcon(white_icon)
        self.btn_start_upload.setIconSize(QSize(20, 20))
        self.btn_start_upload.clicked.connect(self.on_start_upload_clicked)

        # 创建进度条
        self.dh_progress_bar = QProgressBar()
        self.dh_progress_bar.setMinimum(0)
        self.dh_progress_bar.setMaximum(100)
        self.dh_progress_bar.setValue(0)
        self.dh_progress_bar.setVisible(False)

        # 创建定时任务按钮
        self.btn_schedule_task = QPushButton("定时任务")
        self.btn_schedule_task.setToolTip("设置定时自动上传")
        self.btn_schedule_task.setProperty("class", "primaryButton")
        self.btn_schedule_task.setFixedWidth(120)

        # 创建定时任务图标
        schedule_icon = self.get_icon("schedule.svg")
        if not schedule_icon.isNull():
            schedule_pixmap = schedule_icon.pixmap(QSize(20, 20))
            schedule_painter = QPainter(schedule_pixmap)
            schedule_painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
            schedule_painter.fillRect(schedule_pixmap.rect(), QColor(255, 255, 255))
            schedule_painter.end()
            schedule_white_icon = QIcon(schedule_pixmap)
            self.btn_schedule_task.setIcon(schedule_white_icon)
        self.btn_schedule_task.setIconSize(QSize(20, 20))
        self.btn_schedule_task.clicked.connect(self.on_schedule_task_clicked)

        # 添加到布局
        controls_layout.addWidget(self.btn_start_upload)
        controls_layout.addWidget(self.dh_progress_bar, 1)  # 进度条占用剩余空间
        controls_layout.addStretch()
        controls_layout.addWidget(self.btn_schedule_task)

        return bottom_controls

    def create_digital_human_log(self):
        """创建数字人日志区域"""
        log_container = QWidget()
        log_container.setObjectName("logContainer")
        log_layout = QVBoxLayout(log_container)
        log_layout.setContentsMargins(24, 0, 24, 16)
        log_layout.setSpacing(8)

        # 创建日志标题和控制按钮
        log_header = QWidget()
        log_header_layout = QHBoxLayout(log_header)
        log_header_layout.setContentsMargins(0, 0, 0, 0)
        log_header_layout.setSpacing(8)

        # 日志标题
        log_title = QLabel("数字人上传日志")
        log_title.setObjectName("logTitle")
        log_header_layout.addWidget(log_title)

        log_header_layout.addStretch()

        # 展开/收起按钮
        self.dh_log_toggle_btn = QPushButton("收起")
        self.dh_log_toggle_btn.setObjectName("logToggleButton")
        self.dh_log_toggle_btn.setFixedSize(50, 24)
        self.dh_log_toggle_btn.clicked.connect(self.toggle_dh_log)
        log_header_layout.addWidget(self.dh_log_toggle_btn)

        log_layout.addWidget(log_header)

        # 创建日志文本区域
        self.dh_log_text = QTextEdit()
        self.dh_log_text.setObjectName("logText")
        self.dh_log_text.setMaximumHeight(200)
        self.dh_log_text.setMinimumHeight(120)
        self.dh_log_text.setReadOnly(True)
        log_layout.addWidget(self.dh_log_text)

        return log_container

    def toggle_dh_log(self):
        """切换数字人日志显示状态"""
        if self.dh_log_text.isVisible():
            self.dh_log_text.hide()
            self.dh_log_toggle_btn.setText("展开")
        else:
            self.dh_log_text.show()
            self.dh_log_toggle_btn.setText("收起")

    def append_dh_log(self, message):
        """添加数字人日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        # 如果UI还没有完全初始化，使用print输出
        if not hasattr(self, 'dh_log_text') or self.dh_log_text is None:
            print(f"[DH_LOG] {formatted_message}")
            return

        self.dh_log_text.append(formatted_message)

        # 自动滚动到底部
        scrollbar = self.dh_log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    # 声音管理页面创建方法
    def create_voice_management_toolbar(self):
        """创建声音管理工具栏"""
        toolbar_container = QWidget()
        toolbar_container.setObjectName("toolbarContainer")
        toolbar_layout = QHBoxLayout(toolbar_container)
        toolbar_layout.setContentsMargins(0, 8, 16, 8)
        toolbar_layout.setSpacing(16)

        # 创建搜索框容器
        search_container = QWidget()
        search_container.setObjectName("searchWidget")
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(24, 0, 12, 0)
        search_layout.setSpacing(8)

        # 搜索输入框
        self.vm_search_box = QLineEdit()
        self.vm_search_box.setObjectName("searchBox")
        self.vm_search_box.setPlaceholderText("搜索声音模型...")
        # 启用自动搜索
        self.vm_search_box.textChanged.connect(self.on_vm_search_text_changed)
        self.vm_search_box.returnPressed.connect(self.on_vm_search_clicked)
        search_layout.addWidget(self.vm_search_box)

        # 创建清除按钮
        clear_button = QPushButton()
        clear_button.setObjectName("searchButton")
        clear_button.setIcon(self.get_icon("clear.svg"))
        clear_button.setIconSize(QSize(16, 16))
        clear_button.setFixedSize(24, 24)
        clear_button.setToolTip("清除搜索")
        clear_button.clicked.connect(self.clear_vm_search)
        search_layout.addWidget(clear_button)

        # 上一个结果按钮（向左箭头）
        prev_button = QPushButton()
        prev_button.setObjectName("prevSearchButton")
        prev_button.setIcon(self.get_icon("arrow_forward.svg"))  # 向左箭头
        prev_button.setIconSize(QSize(16, 16))
        prev_button.setFixedSize(24, 24)
        prev_button.setToolTip("上一个结果")
        prev_button.clicked.connect(lambda: self.vm_search_table(prev=True))
        search_layout.addWidget(prev_button)

        # 下一个结果按钮（向右箭头）
        next_button = QPushButton()
        next_button.setObjectName("nextSearchButton")
        next_button.setIcon(self.get_icon("arrow_back.svg"))  # 向右箭头
        next_button.setIconSize(QSize(16, 16))
        next_button.setFixedSize(24, 24)
        next_button.setToolTip("下一个结果")
        next_button.clicked.connect(lambda: self.vm_search_table(next=True))
        search_layout.addWidget(next_button)

        toolbar_layout.addWidget(search_container)
        toolbar_layout.addStretch()

        # 功能按钮区域
        buttons_container = QWidget()
        buttons_layout = QHBoxLayout(buttons_container)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(12)

        # 音频位置按钮（使用输出位置图标）
        self.btn_audio_location = QPushButton("音频位置")
        self.btn_audio_location.setObjectName("secondaryButton")
        self.btn_audio_location.setToolTip("打开音频文件夹")
        self.btn_audio_location.setFixedWidth(120)
        self.btn_audio_location.setIcon(self.get_icon("output_folder.svg"))
        self.btn_audio_location.setIconSize(QSize(20, 20))
        self.btn_audio_location.clicked.connect(self.on_audio_location_clicked)
        buttons_layout.addWidget(self.btn_audio_location)

        # 获取ID按钮（使用刷新声音图标）
        self.btn_fetch_ids = QPushButton("获取ID")
        self.btn_fetch_ids.setObjectName("secondaryButton")
        self.btn_fetch_ids.setToolTip("从Fish Audio获取声音模型ID")
        self.btn_fetch_ids.setFixedWidth(120)
        self.btn_fetch_ids.setIcon(self.get_icon("refresh_voice.svg"))
        self.btn_fetch_ids.setIconSize(QSize(20, 20))
        self.btn_fetch_ids.clicked.connect(self.on_fetch_ids_clicked)
        buttons_layout.addWidget(self.btn_fetch_ids)

        toolbar_layout.addWidget(buttons_container)

        return toolbar_container

    def create_voice_management_table(self):
        """创建声音管理表格"""
        table_container = QWidget()
        table_container.setObjectName("tableContainer")
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(24, 16, 24, 16)
        table_layout.setSpacing(0)

        # 创建表格
        self.vm_table = QTableWidget()
        self.vm_table.setObjectName("dataTable")

        # 设置表格边框样式
        self.vm_table.setStyleSheet("""
            QTableWidget#dataTable {
                border: 1px solid #e5e7eb;
                border-radius: 12px;
                background-color: #ffffff;
                gridline-color: #f3f4f6;
                selection-background-color: #eff6ff;
            }
        """)

        # 设置表格列
        headers = ["序号", "名称", "模型ID", "网址", "提取时间"]
        self.vm_table.setColumnCount(len(headers))
        self.vm_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.vm_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.vm_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.vm_table.setAlternatingRowColors(True)
        self.vm_table.setSortingEnabled(True)

        # 设置列宽
        header = self.vm_table.horizontalHeader()
        header.setStretchLastSection(True)  # 最后一列自动拉伸

        # 设置各列宽度
        self.vm_table.setColumnWidth(0, 60)   # 序号
        self.vm_table.setColumnWidth(1, 250)  # 名称
        self.vm_table.setColumnWidth(2, 200)  # 模型ID
        self.vm_table.setColumnWidth(3, 250)  # 网址
        # 提取时间列自动拉伸

        # 设置行高
        self.vm_table.verticalHeader().setDefaultSectionSize(40)
        self.vm_table.verticalHeader().setVisible(False)

        table_layout.addWidget(self.vm_table)

        return table_container

    def create_voice_management_bottom_controls(self):
        """创建声音管理底部控制区"""
        bottom_controls = QWidget()
        bottom_controls.setObjectName("bottomControls")
        controls_layout = QHBoxLayout(bottom_controls)
        controls_layout.setContentsMargins(24, 12, 24, 12)
        controls_layout.setSpacing(16)

        # 创建开始上传按钮
        self.btn_start_voice_upload = QPushButton("开始上传")
        self.btn_start_voice_upload.setToolTip("开始上传音频到Fish Audio平台进行声音克隆")
        self.btn_start_voice_upload.setProperty("class", "primaryButton")
        self.btn_start_voice_upload.setFixedWidth(120)
        self.btn_start_voice_upload.clicked.connect(self.on_start_voice_upload_clicked)
        controls_layout.addWidget(self.btn_start_voice_upload)

        # 添加进度条
        self.vm_progress_bar = QProgressBar()
        self.vm_progress_bar.setObjectName("progressBar")
        self.vm_progress_bar.setVisible(False)  # 初始隐藏
        self.vm_progress_bar.setFixedWidth(200)
        self.vm_progress_bar.setFixedHeight(24)
        self.vm_progress_bar.setTextVisible(True)
        controls_layout.addWidget(self.vm_progress_bar)

        # 添加弹性空间
        controls_layout.addStretch()

        # 进度信息标签
        self.vm_progress_label = QLabel("")
        self.vm_progress_label.setObjectName("progressLabel")
        controls_layout.addWidget(self.vm_progress_label)

        return bottom_controls

    def create_voice_management_log(self):
        """创建声音管理日志区域"""
        log_container = QWidget()
        log_container.setObjectName("logContainer")
        log_layout = QVBoxLayout(log_container)
        log_layout.setContentsMargins(24, 0, 24, 16)
        log_layout.setSpacing(8)

        # 创建日志标题和控制按钮
        log_header = QWidget()
        log_header_layout = QHBoxLayout(log_header)
        log_header_layout.setContentsMargins(0, 0, 0, 0)
        log_header_layout.setSpacing(8)

        # 日志标题
        log_title = QLabel("声音管理日志")
        log_title.setObjectName("logTitle")
        log_header_layout.addWidget(log_title)

        log_header_layout.addStretch()

        # 展开/收起按钮
        self.vm_log_toggle_btn = QPushButton("收起")
        self.vm_log_toggle_btn.setObjectName("logToggleButton")
        self.vm_log_toggle_btn.setFixedSize(50, 24)
        self.vm_log_toggle_btn.clicked.connect(self.toggle_vm_log)
        log_header_layout.addWidget(self.vm_log_toggle_btn)

        log_layout.addWidget(log_header)

        # 创建日志文本区域
        self.vm_log_text = QTextEdit()
        self.vm_log_text.setObjectName("logText")
        self.vm_log_text.setMaximumHeight(200)
        self.vm_log_text.setMinimumHeight(120)
        self.vm_log_text.setReadOnly(True)

        # 设置日志区域边框样式
        self.vm_log_text.setStyleSheet("""
            QTextEdit#logText {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                background-color: #ffffff;
                padding: 8px;
                font-family: "Consolas", "Monaco", "Courier New", monospace;
                font-size: 12px;
                line-height: 1.4;
            }
        """)

        log_layout.addWidget(self.vm_log_text)

        return log_container

    def toggle_vm_log(self):
        """切换声音管理日志显示状态"""
        if self.vm_log_text.isVisible():
            self.vm_log_text.hide()
            self.vm_log_toggle_btn.setText("展开")
        else:
            self.vm_log_text.show()
            self.vm_log_toggle_btn.setText("收起")

    def append_vm_log(self, message):
        """添加声音管理日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        # 如果UI还没有完全初始化，使用print输出
        if not hasattr(self, 'vm_log_text') or self.vm_log_text is None:
            print(f"[VM_LOG] {formatted_message}")
            return

        self.vm_log_text.append(formatted_message)

        # 自动滚动到底部
        scrollbar = self.vm_log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    # 数字人页面事件处理函数
    @Slot()
    def on_load_audio_clicked(self):
        """今日任务按钮点击处理"""
        if not self.digital_human_manager:
            QMessageBox.warning(self, "操作失败", "数字人管理器未初始化，请重启程序。")
            return

        self.append_dh_log("开始扫描音频文件...")

        # 扫描音频文件，启用自动创建目录
        folders = self.digital_human_manager.scan_audio_files(auto_create_dirs=True)

        if folders:
            total_files = sum(len(folder["files"]) for folder in folders)
            self.append_dh_log(f"发现 {total_files} 个音频文件待上传:")

            for folder_info in folders:
                if folder_info["mode"] == "jf":
                    mode_name = "积分模式"
                elif folder_info["mode"] == "quick":
                    mode_name = "快速模式"
                elif folder_info["mode"] == "ah":
                    mode_name = "暗黑模式"
                else:
                    mode_name = f"未知模式({folder_info['mode']})"

                file_count = len(folder_info["files"])
                self.append_dh_log(f"  - {mode_name}: {file_count} 个文件")

                # 显示前几个文件名作为示例
                for i, filename in enumerate(folder_info["files"][:3]):
                    self.append_dh_log(f"    {i+1}. {filename}")
                if len(folder_info["files"]) > 3:
                    self.append_dh_log(f"    ... 还有 {len(folder_info['files']) - 3} 个文件")
        else:
            self.append_dh_log("未发现待上传的音频文件")
            self.append_dh_log("请检查音频文件夹: feiyingshuziren/音频文件/积分/ 和 feiyingshuziren/音频文件/暗黑/")

        # 同时加载已有的表格数据
        self.load_dh_table_data()

    @Slot()
    def on_dh_output_clicked(self):
        """音频位置按钮点击处理"""
        from datetime import datetime
        import os

        # 首先确保音频文件夹结构存在（积分和暗黑文件夹）
        self.append_dh_log("检查并创建音频文件夹结构...")
        self.digital_human_manager.ensure_audio_directories()

        # 使用音频文件目录（这是存放待上传音频的地方）
        folder = self.digital_human_manager.AUDIO_BASE_DIR

        # 如果没有该文件夹，创建它
        if not os.path.exists(folder):
            try:
                os.makedirs(folder, exist_ok=True)
                self.append_dh_log(f"已创建文件夹: {folder}")
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "错误",
                    f"无法创建输出文件夹：{str(e)}"
                )
                return

        # 打开文件夹
        try:
            if sys.platform == 'win32':
                os.startfile(folder)
            elif sys.platform == 'darwin':  # macOS
                import subprocess
                subprocess.Popen(['open', folder])
            else:  # Linux
                import subprocess
                subprocess.Popen(['xdg-open', folder])

            self.append_dh_log(f"已打开音频文件夹: {folder}")
        except Exception as e:
            QMessageBox.critical(
                self,
                "错误",
                f"无法打开文件夹：{str(e)}"
            )

    @Slot()
    def on_update_auth_clicked(self):
        """更新授权按钮点击处理"""
        self.append_dh_log("更新授权功能暂未实现")
        QMessageBox.information(
            self,
            "功能提示",
            "更新授权功能将在后续版本中实现\n\n当前可以手动将认证文件放置到:\nfeiyingshuziren/essential_auth_data.json"
        )

    @Slot()
    def on_start_upload_clicked(self):
        """开始上传/停止上传按钮点击处理"""
        if not self.digital_human_manager:
            QMessageBox.warning(self, "操作失败", "数字人管理器未初始化，请重启程序。")
            return

        if self.digital_human_manager.is_uploading:
            # 如果正在上传，则停止上传
            reply = QMessageBox.question(
                self,
                "确认停止",
                "确定要停止当前的上传任务吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.digital_human_manager.stop_upload()
                self.btn_start_upload.setEnabled(True)
                self.btn_start_upload.setText("开始上传")
            return

        # 检查是否有音频文件，启用自动创建目录
        folders = self.digital_human_manager.scan_audio_files(auto_create_dirs=True)
        if not folders:
            self.append_dh_log("未发现待上传的音频文件")
            QMessageBox.information(
                self,
                "提示",
                "未发现待上传的音频文件\n\n音频文件夹已自动创建，请将音频文件放入以下文件夹：\n- feiyingshuziren/音频文件/积分/\n- feiyingshuziren/音频文件/暗黑/"
            )
            return

        # 检查认证文件
        if not os.path.exists(self.digital_human_manager.AUTH_FILE):
            self.append_dh_log("错误: 认证文件不存在，请先更新授权信息")
            QMessageBox.warning(
                self,
                "认证文件缺失",
                "认证文件不存在，请先点击'更新授权'按钮获取HiFly平台的登录信息"
            )
            return

        # 确认开始上传
        total_files = sum(len(folder["files"]) for folder in folders)
        reply = QMessageBox.question(
            self,
            "确认上传",
            f"发现 {total_files} 个音频文件待上传\n\n是否开始上传到HiFly平台？\n\n注意：上传过程将使用无头模式运行浏览器",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            self.append_dh_log(f"开始上传 {total_files} 个音频文件...")
            self.btn_start_upload.setEnabled(True)  # 保持启用状态以便停止
            self.btn_start_upload.setText("停止上传")

            # 开始上传任务（使用配置中的无头模式设置）
            headless_mode = self.config_manager.get("headless_mode", True)
            self.digital_human_manager.start_upload(headless=headless_mode)

            # 监听上传完成事件
            if hasattr(self.digital_human_manager, 'upload_thread'):
                self.digital_human_manager.upload_thread.finished.connect(self.on_upload_task_finished)
        else:
            self.append_dh_log("用户取消了上传操作")

    def on_upload_task_finished(self):
        """上传任务完成处理"""
        self.btn_start_upload.setEnabled(True)
        self.btn_start_upload.setText("开始上传")
        self.append_dh_log("上传任务已完成")

        # 刷新表格数据
        self.load_dh_table_data()



    @Slot()
    def on_schedule_task_clicked(self):
        """定时任务按钮点击处理 - 打开新的定时任务管理器"""
        dialog = ScheduleManagerDialog(self, self.schedule_manager)
        dialog.exec()

    def update_feather_visibility(self):
        """根据视频处理方案显示/隐藏羽化设置"""
        if hasattr(self, 'feather_group') and hasattr(self, 'cmb_video_process_mode'):
            current_mode = self.cmb_video_process_mode.currentData()
            is_watermark_removal = current_mode == "watermark_removal"
            self.feather_group.setVisible(is_watermark_removal)

    def on_reset_feather_params(self):
        """重置羽化参数为默认值"""
        self.feather_radius_spin.setValue(3)
        self.feather_sigma_spin.setValue(1.0)
        self.edge_blur_spin.setValue(8)

        # 立即保存到配置
        self.config_manager.set("feather_radius", 3)
        self.config_manager.set("feather_sigma", 1.0)
        self.config_manager.set("edge_blur_radius", 8)

        QMessageBox.information(self, "重置完成", "羽化参数已重置为默认值")


























    def update_dh_progress(self, value):
        """更新数字人进度条"""
        self.dh_progress_bar.setValue(value)
        if value > 0:
            self.dh_progress_bar.setVisible(True)
        else:
            self.dh_progress_bar.setVisible(False)

    def on_dh_upload_completed(self, filename, status, message):
        """数字人上传完成处理"""
        self.append_dh_log(f"文件 {filename} 上传{status}: {message}")

    def on_schedule_task_triggered(self, task_id):
        """定时任务触发处理"""
        if task_id in self.schedule_manager.tasks:
            task = self.schedule_manager.tasks[task_id]
            self.append_dh_log(f"定时任务触发：{task.name}")

            # 执行数字人上传检查
            if hasattr(self.digital_human_manager, 'check_and_upload'):
                self.digital_human_manager.check_and_upload()

    def load_dh_table_data(self):
        """加载数字人表格数据"""
        try:
            # 确保今日任务文件夹和表格存在
            main_folder, table_path = self.digital_human_manager.ensure_today_folder_and_table()

            if not main_folder or not table_path:
                self.append_dh_log("创建今日任务环境失败")
                return

            if os.path.exists(table_path):
                import pandas as pd
                df = pd.read_excel(table_path, engine='openpyxl')

                # 清空表格
                self.dh_table.setRowCount(0)

                # 填充数据
                for index, row in df.iterrows():
                    row_position = self.dh_table.rowCount()
                    self.dh_table.insertRow(row_position)

                    # 序号
                    self.dh_table.setItem(row_position, 0, QTableWidgetItem(str(row_position + 1)))

                    # 其他列数据
                    columns = ["名称", "素材ID", "生成模式", "上传情况", "上传时间", "文件夹", "工单号", "任务ID", "版型", "完成状态", "完成日期"]
                    for col_index, col_name in enumerate(columns, 1):
                        value = str(row.get(col_name, "")) if pd.notna(row.get(col_name)) else ""
                        self.dh_table.setItem(row_position, col_index, QTableWidgetItem(value))

                    # 添加删除按钮
                    delete_btn = QPushButton("删除")
                    delete_btn.setObjectName("deleteButton")
                    delete_btn.setMaximumWidth(60)
                    delete_btn.clicked.connect(lambda checked, r=row_position: self.delete_dh_row(r))
                    self.dh_table.setCellWidget(row_position, len(columns) + 1, delete_btn)

                self.append_dh_log(f"已加载 {len(df)} 条数字人记录")
            else:
                # 这种情况理论上不应该发生，因为init_result_table会创建文件
                self.append_dh_log("数字人记录表格初始化完成，等待上传数据")

        except Exception as e:
            self.append_dh_log(f"加载数字人表格数据失败: {e}")

    @Slot()
    def on_dh_search_clicked(self):
        """数字人搜索按钮点击处理"""
        search_text = self.dh_search_box.text().strip()
        if not search_text:
            # 如果搜索框为空，显示所有行
            for row in range(self.dh_table.rowCount()):
                self.dh_table.setRowHidden(row, False)
            self.append_dh_log("已清除搜索过滤")
            return

        # 搜索并过滤表格
        found_count = 0
        for row in range(self.dh_table.rowCount()):
            row_matches = False
            # 检查每一列是否包含搜索文本
            for col in range(self.dh_table.columnCount()):
                item = self.dh_table.item(row, col)
                if item and search_text.lower() in item.text().lower():
                    row_matches = True
                    break

            # 设置行的可见性
            self.dh_table.setRowHidden(row, not row_matches)
            if row_matches:
                found_count += 1

        self.append_dh_log(f"搜索 '{search_text}' 找到 {found_count} 条记录")

    @Slot()
    def on_update_auth_clicked(self):
        """更新授权按钮点击处理"""
        try:
            self.append_dh_log("开始更新授权信息...")

            # 创建并显示登录对话框
            login_dialog = LoginDialog(self)
            result = login_dialog.exec()

            if result == QDialog.Accepted and login_dialog.login_success:
                # 登录成功，保存认证数据
                if login_dialog.auth_data:
                    self.save_auth_data(login_dialog.auth_data)
                    self.append_dh_log("✓ 授权信息更新成功")
                    QMessageBox.information(
                        self,
                        "更新成功",
                        "授权信息已成功更新！\n\n现在可以开始上传数字人音频文件了。"
                    )
                else:
                    self.append_dh_log("✗ 未获取到有效的授权信息")
                    QMessageBox.warning(
                        self,
                        "更新失败",
                        "未获取到有效的授权信息，请重试。"
                    )
            else:
                self.append_dh_log("用户取消了授权更新")

        except Exception as e:
            self.append_dh_log(f"更新授权失败: {e}")
            QMessageBox.critical(
                self,
                "错误",
                f"更新授权失败：{e}"
            )

    @Slot()
    def on_video_download_clicked(self):
        """视频下载按钮点击处理"""
        try:
            self.append_dh_log("开始准备视频下载任务...")
            
            # 检查是否正在进行其他任务
            if hasattr(self, 'video_download_worker') and self.video_download_worker and self.video_download_worker.isRunning():
                reply = QMessageBox.question(
                    self,
                    "确认停止",
                    "视频下载正在进行中，是否停止当前下载？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    self.video_downloader.stop_download()
                    self.video_download_worker.quit()
                    self.video_download_worker.wait()
                    self.btn_video_download.setText("视频下载")
                    self.btn_video_download.setEnabled(True)
                    self.append_dh_log("视频下载已停止")
                
                return
            
            # 创建视频下载器
            from core.video_downloader import VideoDownloader, VideoDownloadWorker
            
            if not hasattr(self, 'video_downloader') or not self.video_downloader:
                self.video_downloader = VideoDownloader()
                
                # 连接信号
                self.video_downloader.log_message.connect(self.append_dh_log)
                self.video_downloader.progress_updated.connect(self.update_download_progress)
                self.video_downloader.download_completed.connect(self.on_video_download_item_completed)
                self.video_downloader.all_completed.connect(self.on_all_video_downloads_completed)
            
            # 从配置中获取下载设置
            concurrent_browsers = self.config_manager.get("video_download_concurrent_browsers", 3)
            search_days = self.config_manager.get("video_download_search_days", 3)
            enable_screenshots = self.config_manager.get("video_download_enable_screenshots", False)
            headless_mode = self.config_manager.get("headless_mode", True)  # 使用数字人的无头模式设置
            
            # 配置下载器
            self.video_downloader.set_config(
                concurrent_browsers=concurrent_browsers,
                search_days=search_days,
                enable_screenshots=enable_screenshots,
                headless_mode=headless_mode
            )
            
            # 创建工作线程
            self.video_download_worker = VideoDownloadWorker(self.video_downloader)
            
            # 连接线程完成信号
            self.video_download_worker.finished.connect(self.on_video_download_thread_finished)
            
            # 更新按钮状态
            self.btn_video_download.setText("停止下载")
            self.btn_video_download.setEnabled(True)
            
            # 启动下载线程
            self.video_download_worker.start()
            
            self.append_dh_log(f"视频下载任务已启动 (并发浏览器: {concurrent_browsers}, 搜索天数: {search_days}, 无头模式: {headless_mode})")
            
        except Exception as e:
            self.append_dh_log(f"启动视频下载失败: {str(e)}")
            QMessageBox.critical(
                self,
                "错误",
                f"启动视频下载失败：{str(e)}"
            )
    
    @Slot(int, int)
    def update_download_progress(self, current, total):
        """更新下载进度"""
        progress_text = f"下载进度: {current}/{total}"
        self.append_dh_log(progress_text)
    
    @Slot(str, bool, str)
    def on_video_download_item_completed(self, video_name, success, message):
        """单个视频下载完成"""
        status = "✓" if success else "✗"
        self.append_dh_log(f"{status} {video_name}: {message}")
    
    @Slot(dict)
    def on_all_video_downloads_completed(self, results):
        """所有视频下载完成"""
        success_count = results.get("成功", 0)
        failed_count = results.get("失败", 0)
        total_count = results.get("总计", 0)
        
        self.append_dh_log("=" * 50)
        self.append_dh_log("视频下载任务完成！")
        self.append_dh_log(f"总计: {total_count} 个视频")
        self.append_dh_log(f"成功: {success_count} 个")
        self.append_dh_log(f"失败: {failed_count} 个")
        
        if success_count > 0:
            self.append_dh_log("\n成功下载的视频:")
            for video_name in results.get("成功列表", []):
                self.append_dh_log(f"  ✓ {video_name}")
        
        if failed_count > 0:
            self.append_dh_log("\n下载失败的视频:")
            for video_name in results.get("失败列表", []):
                self.append_dh_log(f"  ✗ {video_name}")
        
        self.append_dh_log("=" * 50)
        
        # 显示完成对话框
        QMessageBox.information(
            self,
            "下载完成",
            f"视频下载任务已完成！\n\n"
            f"总计: {total_count} 个视频\n"
            f"成功: {success_count} 个\n"
            f"失败: {failed_count} 个\n\n"
            f"详细信息请查看日志。"
        )
    
    @Slot()
    def on_video_download_thread_finished(self):
        """视频下载线程完成"""
        self.btn_video_download.setText("视频下载")
        self.btn_video_download.setEnabled(True)
        
        # 清理工作线程
        if hasattr(self, 'video_download_worker') and self.video_download_worker:
            self.video_download_worker.deleteLater()
            self.video_download_worker = None



    @Slot(QPoint)
    def show_dh_context_menu(self, position):
        """显示数字人表格右键菜单"""
        if self.dh_table.itemAt(position) is None:
            return

        menu = QMenu(self)

        # 删除选中行动作
        delete_action = QAction("删除选中行", self)
        delete_action.setIcon(self.get_icon("delete.svg"))
        delete_action.triggered.connect(self.delete_selected_dh_rows)
        menu.addAction(delete_action)

        # 显示菜单
        menu.exec_(self.dh_table.mapToGlobal(position))

    @Slot()
    def delete_selected_dh_rows(self):
        """删除选中的数字人表格行"""
        try:
            selected_rows = set()
            for item in self.dh_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要删除的行")
                return

            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除选中的 {len(selected_rows)} 行记录吗？\n此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 获取要删除的行信息（包含更多唯一标识）
                rows_to_delete = []
                for row in selected_rows:
                    name_item = self.dh_table.item(row, 1)  # 名称列
                    task_id_item = self.dh_table.item(row, 2)  # 任务ID列
                    status_item = self.dh_table.item(row, 3)  # 状态列

                    if name_item:
                        row_info = {
                            'name': name_item.text(),
                            'task_id': task_id_item.text() if task_id_item else '',
                            'status': status_item.text() if status_item else '',
                            'ui_row': row  # UI中的行号
                        }
                        rows_to_delete.append(row_info)

                # 从Excel文件中删除记录
                if self.digital_human_manager:
                    success = self.digital_human_manager.delete_records_by_row_info(rows_to_delete)
                    if success:
                        # 重新加载表格数据
                        self.load_dh_table_data()
                        self.append_dh_log(f"✓ 已删除 {len(selected_rows)} 条记录")
                    else:
                        QMessageBox.warning(self, "错误", "删除记录失败")
                else:
                    QMessageBox.warning(self, "错误", "数字人管理器未初始化")

        except Exception as e:
            self.append_dh_log(f"❌ 删除记录失败: {e}")
            QMessageBox.critical(self, "错误", f"删除记录失败：{e}")

    def delete_dh_row(self, row):
        """删除指定行"""
        try:
            name_item = self.dh_table.item(row, 1)  # 名称列
            if not name_item:
                return

            file_name = name_item.text()

            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除记录 '{file_name}' 吗？\n此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                if self.digital_human_manager:
                    # 获取行的详细信息
                    task_id_item = self.dh_table.item(row, 2)  # 任务ID列
                    status_item = self.dh_table.item(row, 3)  # 状态列

                    row_info = {
                        'name': file_name,
                        'task_id': task_id_item.text() if task_id_item else '',
                        'status': status_item.text() if status_item else '',
                        'ui_row': row
                    }

                    success = self.digital_human_manager.delete_records_by_row_info([row_info])
                    if success:
                        # 重新加载表格数据
                        self.load_dh_table_data()
                        self.append_dh_log(f"✓ 已删除记录: {file_name}")
                    else:
                        QMessageBox.warning(self, "错误", "删除记录失败")
                else:
                    QMessageBox.warning(self, "错误", "数字人管理器未初始化")

        except Exception as e:
            self.append_dh_log(f"❌ 删除记录失败: {e}")
            QMessageBox.critical(self, "错误", f"删除记录失败：{e}")

    def save_auth_data(self, auth_data):
        """保存认证数据到文件"""
        try:
            import json
            auth_file = self.digital_human_manager.AUTH_FILE

            with open(auth_file, "w", encoding="utf-8") as f:
                json.dump(auth_data, f, ensure_ascii=False, indent=2)

            self.append_dh_log(f"✓ 认证数据已保存到 {auth_file}")

            # 显示token信息（隐藏敏感部分）
            token = auth_data.get("localStorage", {}).get("token", "")
            if token:
                display_token = token[:10] + "..." if len(token) > 10 else token
                self.append_dh_log(f"✓ 获取到用户token: {display_token}")

        except Exception as e:
            self.append_dh_log(f"保存认证数据失败: {e}")
            raise e







    def toggle_maximize(self):
        """切换窗口最大化状态"""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()
    
    def create_statusbar(self):
        """创建状态栏"""
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        
        # 创建剪贴板监听切换按钮
        self.btn_toggle_clipboard = QPushButton("剪贴板监听: 关闭")
        self.btn_toggle_clipboard.setObjectName("btn_toggle_clipboard")
        self.btn_toggle_clipboard.setCheckable(True)
        self.btn_toggle_clipboard.setChecked(False)
        self.btn_toggle_clipboard.clicked.connect(self.toggle_clipboard_monitoring)
        
        # 添加到状态栏
        self.statusBar.addPermanentWidget(self.btn_toggle_clipboard)
    
    def init_clipboard_monitoring(self):
        """初始化剪贴板监听"""
        # 检查配置是否启用剪贴板监听
        enable_monitoring = self.config_manager.get("enable_clipboard_monitoring", True)
        
        if enable_monitoring:
            self.btn_toggle_clipboard.setChecked(True)
            self.toggle_clipboard_monitoring(True)
    
    def update_theme_mode(self):
        """更新主题模式相关设置"""
        # 设置属性
        self.setProperty("darkMode", self.dark_mode)

        # 更新侧边栏属性
        self.sidebar.setProperty("darkMode", self.dark_mode)

        # 应用设置页面样式
        self.apply_settings_styles()

        # 应用标题栏样式
        self.apply_title_bar_styles()

        # 刷新样式
        self.style().unpolish(self)
        self.style().polish(self)

        # 递归更新所有子部件
        self.update_widget_style(self)

    def apply_settings_styles(self):
        """应用设置页面样式"""
        if hasattr(self, 'settings_container'):
            # 获取动态图标路径
            arrow_down_path = self.get_icon_path("arrow_down.svg").replace("\\", "/")

            self.settings_container.setStyleSheet("""
                /* 设置页面整体样式 */
                QWidget#settingsPage {
                    background-color: white;
                    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
                }

                /* 滚动区域样式 */
                QScrollArea {
                    background-color: white;
                    border: none;
                }

                /* 滚动区域视口样式 */
                QScrollArea > QWidget > QWidget {
                    background-color: white;
                }

                /* 页面标题样式 */
                QLabel#pageTitle {
                    font-size: 24px;
                    font-weight: bold;
                    color: #333333;
                    margin-bottom: 15px;
                    margin-left: 0px;
                    padding: 0;
                }

                /* 设置组样式 */
                QGroupBox#settingsGroup {
                    font-size: 14px;
                    font-weight: normal;
                    color: #666666;
                    background-color: #fafafa;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    margin-bottom: 15px;
                    padding-top: 10px;
                }

                QGroupBox#settingsGroup::title {
                    subcontrol-origin: margin;
                    subcontrol-position: top left;
                    padding: 0 8px;
                    background-color: transparent;
                    border: none;
                    margin-left: -8px;
                    margin-top: 0px;
                    margin-bottom: 28px;
                    color: #666666;
                    font-size: 14px;
                    font-weight: bold;
                }

                /* 标签样式 */
                QLabel#settingsLabel {
                    font-size: 14px;
                    color: #333333;
                    font-weight: normal;
                }

                /* 输入框样式 */
                QLineEdit#settingsLineEdit {
                    padding: 8px 12px;
                    border: 1px solid #d1d5db;
                    border-radius: 4px;
                    background-color: white;
                    font-size: 14px;
                    min-height: 16px;
                }

                QLineEdit#settingsLineEdit:focus {
                    border-color: #3b82f6;
                    outline: none;
                }

                /* 下拉框样式 */
                QComboBox#settingsComboBox {
                    padding: 8px 12px;
                    border: 1px solid #d1d5db;
                    border-radius: 4px;
                    background-color: white;
                    font-size: 14px;
                    min-height: 16px;
                }

                QComboBox#settingsComboBox:focus {
                    border-color: #3b82f6;
                }

                QComboBox#settingsComboBox::drop-down {
                    border: none;
                    width: 20px;
                    subcontrol-origin: padding;
                    subcontrol-position: top right;
                }

                QComboBox#settingsComboBox::down-arrow {
                    image: url(ARROW_DOWN_PATH_PLACEHOLDER);
                    width: 12px;
                    height: 12px;
                    margin-right: 8px;
                }

                /* 复选框样式 */
                QCheckBox#settingsCheckBox {
                    font-size: 14px;
                    color: #333333;
                    spacing: 8px;
                }

                QCheckBox#settingsCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                    border: 1px solid #d1d5db;
                    border-radius: 3px;
                    background-color: white;
                }

                QCheckBox#settingsCheckBox::indicator:checked {
                    background-color: #3b82f6;
                    border-color: #3b82f6;
                    background-image: none;
                }

                /* 链接按钮样式 */
                QPushButton#linkButton {
                    background-color: #3b82f6;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: normal;
                }

                QPushButton#linkButton:hover {
                    background-color: #2563eb;
                }

                QPushButton#linkButton:pressed {
                    background-color: #1d4ed8;
                }

                /* 主要按钮样式 */
                QPushButton {
                    background-color: #3b82f6;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 10px 20px;
                    font-size: 14px;
                    font-weight: normal;
                    min-width: 80px;
                }

                QPushButton:hover {
                    background-color: #2563eb;
                }

                QPushButton:pressed {
                    background-color: #1d4ed8;
                }

                /* 次要按钮样式 */
                QPushButton#secondaryButton {
                    background-color: #f8f9fa;
                    color: #6c757d;
                    border: 1px solid #d1d5db;
                }

                QPushButton#secondaryButton:hover {
                    background-color: #e9ecef;
                    border-color: #adb5bd;
                }

                QPushButton#secondaryButton:pressed {
                    background-color: #dee2e6;
                }

                /* 按钮容器样式 */
                QWidget#buttonContainer {
                    background-color: #f5f5f5;
                    border-top: 1px solid #e0e0e0;
                }
            """.replace("ARROW_DOWN_PATH_PLACEHOLDER", arrow_down_path))
    
    def update_widget_style(self, widget):
        """递归更新所有子部件的样式"""
        for child in widget.children():
            if isinstance(child, QWidget):
                child.setProperty("darkMode", self.dark_mode)
                child.style().unpolish(child)
                child.style().polish(child)
                self.update_widget_style(child)

    def apply_title_bar_styles(self):
        """应用标题栏样式"""
        if hasattr(self, 'app_title'):
            color = "#ffffff" if self.dark_mode else "#333333"
            self.app_title.setStyleSheet(f"""
                QLabel#appTitle {{
                    color: {color};
                    font-size: 14px;
                    font-weight: 500;
                    margin-left: 4px;
                }}
            """)

    @Slot(bool)
    def on_theme_changed(self, is_dark):
        """主题变更信号处理"""
        self.dark_mode = is_dark
        self.update_theme_mode()
    
    @Slot(bool)
    def toggle_clipboard_monitoring(self, checked):
        """切换剪贴板监听状态"""
        if checked:
            if self.clipboard_manager.start_monitoring():
                self.btn_toggle_clipboard.setText("剪贴板监听: 开启")
                self.statusBar.showMessage("已开启剪贴板监听", 3000)
            else:
                self.btn_toggle_clipboard.setChecked(False)
                self.btn_toggle_clipboard.setText("剪贴板监听: 关闭")
                self.statusBar.showMessage("无法开启剪贴板监听", 3000)
        else:
            if self.clipboard_manager.stop_monitoring():
                self.btn_toggle_clipboard.setText("剪贴板监听: 关闭")
                self.statusBar.showMessage("已关闭剪贴板监听", 3000)
            else:
                self.btn_toggle_clipboard.setChecked(True)
                self.btn_toggle_clipboard.setText("剪贴板监听: 开启")
                self.statusBar.showMessage("无法关闭剪贴板监听", 3000)
        
        # 保存设置
        self.config_manager.set("enable_clipboard_monitoring", checked)
    
    @Slot()
    def on_daily_task_clicked(self):
        """创建并打开今日任务文件"""
        # 使用处理器的create_daily_task方法，它会正确初始化Excel文件
        work_folders = self.processor.create_daily_task(create_download_folder=False)

        main_folder = work_folders["main_folder"]
        excel_file = work_folders["daily_excel"]

        self.append_log(f"创建主文件夹: {main_folder}")

        # 设置处理器的文件路径
        self.processor.current_loaded_file = excel_file
        self.processor.daily_file = excel_file

        # 更新表格显示（create_daily_task已经正确初始化了scripts_df）
        try:
            if self.processor.scripts_df is not None:
                self.update_table_from_dataframe(self.processor.scripts_df)
                self.append_log(f"已加载今日文案文件，共 {len(self.processor.scripts_df)} 条数据")
            else:
                self.append_log("创建今日文案文件失败")
            self.append_log(f"今日任务文件: {excel_file}")
        except Exception as e:
            self.append_log(f"更新表格失败: {str(e)}")
    
    @Slot()
    def on_load_scripts_clicked(self):
        """加载文案脚本"""
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件", "", "Excel Files (*.xlsx *.xls)", options=options
        )
        
        if file_name:
            try:
                self.processor.load_scripts(file_name)
                # 设置当前加载的文件
                self.processor.current_loaded_file = file_name
                
                self.update_table_from_dataframe(self.processor.scripts_df)  # 改为scripts_df
                self.append_log(f"已加载文件: {file_name}，共 {len(self.processor.scripts_df)} 条数据")
            except Exception as e:
                self.append_log(f"加载文件失败: {str(e)}")
    
    @Slot()
    def on_paste_data_clicked(self):
        """粘贴数据按钮点击处理"""
        # 手动处理剪贴板数据
        df = self.clipboard_manager.process_clipboard_data()
        
        if df is None:
            # 检查错误原因
            missing_columns = []
            if config.SCRIPT_TEXT_COLUMN not in self.clipboard_manager.last_columns_found:
                missing_columns.append("克隆文案")
            if config.ACTOR_COLUMN not in self.clipboard_manager.last_columns_found:
                missing_columns.append("演员形象")
            if config.WORK_ORDER_COLUMN not in self.clipboard_manager.last_columns_found:
                missing_columns.append("工单号")
                
            # 确保last_columns_found中的所有元素都是字符串
            available_columns = []
            for col in self.clipboard_manager.last_columns_found:
                available_columns.append(str(col))
                
            if missing_columns:
                # 特定缺少列的错误信息
                QMessageBox.warning(
                    self,
                    "粘贴数据失败",
                    f"缺少必要的列: {', '.join(missing_columns)}。\n\n"
                    f"请确保复制的数据包含这些必要列。\n"
                    f"可用的列: {', '.join(available_columns)}"
                )
            else:
                # 一般性错误信息
                QMessageBox.warning(
                    self,
                    "粘贴数据",
                    "未检测到有效的表格数据。请确保复制时包含表头行（如：工单号、克隆文案、演员形象等必要列）。"
                )
            return
        
        if df is not None and not df.empty:
            try:
                # 先创建工作文件夹（如果不存在）
                try:
                    work_folders = self.config_manager.create_daily_work_folder()
                    self.append_log(f"准备今日工作环境: {work_folders['main_folder']}")
                except Exception as e:
                    self.append_log(f"创建工作文件夹失败: {e}")

                # 过滤数据，只保留必要的列
                filtered_df = self.filter_dataframe_columns(df)
                self.append_log(f"过滤后的数据: {len(filtered_df)}行，{len(filtered_df.columns)}列")

                # 使用过滤后的数据替换原始数据
                df = filtered_df

                # 打印诊断信息
                print(f"粘贴数据: {len(df)}行, {list(df.columns)}")
                if len(df) > 0:
                    print(f"第一行数据: {df.iloc[0].to_dict()}")

                # 在日志中显示粘贴数据的详细信息
                self.append_log(f"检测到剪贴板数据: {len(df)}行")
                self.append_log(f"包含列: {', '.join(df.columns.tolist())}")
                
                # 添加哈希值以便检测重复行
                df_hash = ""
                if len(df) > 0:
                    # 使用第一行的前几个值作为哈希值
                    first_row = df.iloc[0]
                    hash_data = []
                    for col in ['命名', '工单号', '演员形象', '克隆文案']:
                        if col in first_row:
                            val = str(first_row[col])[:20]  # 取前20个字符
                            hash_data.append(val)
                    df_hash = "_".join(hash_data)
                    print(f"新数据哈希值: {df_hash}")
                
                # 检查当前表格数据中是否已有相同数据
                has_duplicate = False
                if self.table_scripts.rowCount() > 0 and df_hash:
                    # 检查现有数据
                    existing_df = self.update_processor_data_from_table()
                    if existing_df is not None and len(existing_df) > 0:
                        for i, row in existing_df.iterrows():
                            row_hash = []
                            for col in ['命名', '工单号', '演员形象', '克隆文案']:
                                if col in row:
                                    val = str(row[col])[:20]  # 取前20个字符
                                    row_hash.append(val)
                            row_hash_str = "_".join(row_hash)
                            print(f"现有行 {i} 哈希值: {row_hash_str}")
                            if row_hash_str == df_hash:
                                has_duplicate = True
                                print(f"发现重复行！行 {i} 与新数据相同")
                                break
                
                # 如果发现重复行，询问用户是否继续
                if has_duplicate:
                    reply = QMessageBox.question(
                        self,
                        "发现重复数据",
                        "粘贴的数据与现有数据中的某行相似。是否仍要添加?",
                        QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                        QMessageBox.No
                    )
                    
                    if reply != QMessageBox.Yes:
                        self.statusBar.showMessage("已取消添加重复数据", 3000)
                        return
                
                # 是否追加数据
                if self.table_scripts.rowCount() > 0:
                    reply = QMessageBox.question(
                        self,
                        "粘贴数据",
                        f"检测到 {len(df)} 条数据，是否要将数据追加到现有数据后？\n选择\"否\"将覆盖现有数据。",
                        QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                        QMessageBox.Yes
                    )
                    
                    if reply == QMessageBox.Cancel:
                        return
                    
                    append = (reply == QMessageBox.Yes)
                else:
                    append = False
                    # 无需追加时也显示提示
                    reply = QMessageBox.information(
                        self,
                        "粘贴数据",
                        f"检测到 {len(df)} 条数据，将导入到表格中。",
                        QMessageBox.Ok
                    )
                
                # 保存当前状态到历史记录
                self.save_current_state()
                
                # 更新表格
                self.update_table_from_dataframe(df, append=append)
                
                # 更新处理器数据
                if append and self.processor.scripts_df is not None:
                    self.processor.scripts_df = pd.concat([self.processor.scripts_df, df], ignore_index=True)
                else:
                    self.processor.scripts_df = df
                
                # 立即保存到文件
                self.auto_save_to_daily_excel()
                
                # 显示消息
                self.statusBar.showMessage(f"已{'追加' if append else '粘贴'} {len(df)} 条文案", 3000)
                
                # 确保所有行高统一
                self.ensure_uniform_row_height()
            except Exception as e:
                # 显示异常信息
                print(f"粘贴数据处理出错: {e}")
                import traceback
                traceback.print_exc()
                QMessageBox.warning(
                    self,
                    "粘贴数据",
                    f"粘贴数据处理出错: {str(e)}"
                )
        else:
            # 显示错误消息
            QMessageBox.warning(
                self,
                "粘贴数据",
                "剪贴板中没有有效的表格数据。"
            )
    
    @Slot()
    def on_login_clicked(self):
        """用户登录按钮点击处理"""
        # 显示简单的登录对话框
        from PySide6.QtWidgets import QDialog, QFormLayout, QLineEdit, QDialogButtonBox

        dialog = QDialog(self)
        dialog.setWindowTitle("用户登录")
        dialog.setFixedSize(300, 150)

        layout = QFormLayout(dialog)
        username_edit = QLineEdit()
        password_edit = QLineEdit()
        password_edit.setEchoMode(QLineEdit.Password)

        layout.addRow("用户名:", username_edit)
        layout.addRow("密码:", password_edit)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        if dialog.exec() == QDialog.Accepted:
            self.log_message(f"用户登录: {username_edit.text()}")

    @Slot()
    def on_settings_clicked(self):
        """设置按钮点击处理"""
        # 更新侧边栏按钮状态
        self.update_sidebar_button_states("settings")

        # 切换到设置面板 (索引4，正确的设置页面索引)
        self.content_stack.setCurrentIndex(4)
        self.current_content = "settings"

    def update_sidebar_button_states(self, active_button):
        """更新侧边栏按钮的选中状态"""
        # 清除所有按钮的选中状态并恢复原始图标
        if hasattr(self, 'btn_sound_clone'):
            self.btn_sound_clone.setProperty("selected", False)
            self.btn_sound_clone.setIcon(self.get_icon("voice_clone.svg"))  # 恢复原始图标
            self.btn_sound_clone.style().unpolish(self.btn_sound_clone)
            self.btn_sound_clone.style().polish(self.btn_sound_clone)

        if hasattr(self, 'btn_settings'):
            self.btn_settings.setProperty("selected", False)
            self.btn_settings.setIcon(self.get_icon("settings_icon.svg"))  # 恢复原始图标
            self.btn_settings.style().unpolish(self.btn_settings)
            self.btn_settings.style().polish(self.btn_settings)

        if hasattr(self, 'btn_digital_human'):
            self.btn_digital_human.setProperty("selected", False)
            self.btn_digital_human.setIcon(self.get_icon("digital_human.svg"))  # 恢复原始图标
            self.btn_digital_human.style().unpolish(self.btn_digital_human)
            self.btn_digital_human.style().polish(self.btn_digital_human)

        if hasattr(self, 'btn_voice_management'):
            self.btn_voice_management.setProperty("selected", False)
            self.btn_voice_management.setIcon(self.get_icon("voice_management.svg"))  # 恢复原始图标
            self.btn_voice_management.style().unpolish(self.btn_voice_management)
            self.btn_voice_management.style().polish(self.btn_voice_management)

        if hasattr(self, 'btn_video_management'):
            self.btn_video_management.setProperty("selected", False)
            self.btn_video_management.setIcon(self.get_icon("video_management.svg"))  # 恢复原始图标
            self.btn_video_management.style().unpolish(self.btn_video_management)
            self.btn_video_management.style().polish(self.btn_video_management)

        # 设置当前活动按钮为选中状态并切换到蓝色图标
        if active_button == "settings" and hasattr(self, 'btn_settings'):
            self.btn_settings.setProperty("selected", True)
            self.btn_settings.setIcon(self.get_icon("settings_blue.svg"))  # 切换到蓝色图标
            self.btn_settings.style().unpolish(self.btn_settings)
            self.btn_settings.style().polish(self.btn_settings)
        elif active_button == "sound_clone" and hasattr(self, 'btn_sound_clone'):
            self.btn_sound_clone.setProperty("selected", True)
            self.btn_sound_clone.setIcon(self.get_icon("voice_clone_blue.svg"))  # 切换到蓝色图标
            self.btn_sound_clone.style().unpolish(self.btn_sound_clone)
            self.btn_sound_clone.style().polish(self.btn_sound_clone)
        elif active_button == "digital_human" and hasattr(self, 'btn_digital_human'):
            self.btn_digital_human.setProperty("selected", True)
            self.btn_digital_human.setIcon(self.get_icon("digital_human_blue.svg"))  # 切换到蓝色图标
            self.btn_digital_human.style().unpolish(self.btn_digital_human)
            self.btn_digital_human.style().polish(self.btn_digital_human)
        elif active_button == "voice_management" and hasattr(self, 'btn_voice_management'):
            self.btn_voice_management.setProperty("selected", True)
            self.btn_voice_management.setIcon(self.get_icon("voice_management_blue.svg"))  # 切换到蓝色图标
            self.btn_voice_management.style().unpolish(self.btn_voice_management)
            self.btn_voice_management.style().polish(self.btn_voice_management)
        elif active_button == "video_management" and hasattr(self, 'btn_video_management'):
            self.btn_video_management.setProperty("selected", True)
            self.btn_video_management.setIcon(self.get_icon("video_management_blue.svg"))  # 切换到蓝色图标
            self.btn_video_management.style().unpolish(self.btn_video_management)
            self.btn_video_management.style().polish(self.btn_video_management)

    @Slot()
    def on_sound_clone_clicked(self):
        """声音克隆按钮点击处理"""
        # 如果当前已经在声音克隆页面，确保按钮状态正确
        if self.current_content == "main":
            # 仍然需要更新按钮状态，确保UI一致性
            self.update_sidebar_button_states("sound_clone")
            return
            
        # 如果当前在设置页面，检查是否有未保存的更改
        if self.current_content == "settings":
            # 检查设置是否已更改
            if self.settings_have_changed():
                # 弹出确认对话框
                reply = QMessageBox.question(
                    self,
                    "保存设置",
                    "设置已更改，是否保存？",
                    QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
                    QMessageBox.Save
                )
                
                if reply == QMessageBox.Save:
                    # 保存设置
                    self.save_settings()
                elif reply == QMessageBox.Cancel:
                    # 取消返回
                    return
                # 如果选择不保存，直接切换
            
        # 更新侧边栏按钮状态
        self.update_sidebar_button_states("sound_clone")

        # 切换到声音克隆主页面
        self.content_stack.setCurrentIndex(0)
        self.current_content = "main"

    @Slot()
    def on_digital_human_clicked(self):
        """数字人按钮点击处理"""
        # 如果当前已经在数字人页面，确保按钮状态正确
        if self.current_content == "digital_human":
            # 仍然需要更新按钮状态，确保UI一致性
            self.update_sidebar_button_states("digital_human")
            return

        # 如果当前在设置页面，检查是否有未保存的更改
        if self.current_content == "settings":
            # 检查设置是否已更改
            if self.settings_have_changed():
                # 弹出确认对话框
                reply = QMessageBox.question(
                    self,
                    "保存设置",
                    "设置已更改，是否保存？",
                    QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
                    QMessageBox.Save
                )

                if reply == QMessageBox.Save:
                    # 保存设置
                    self.save_settings()
                elif reply == QMessageBox.Cancel:
                    # 取消返回
                    return
                # 如果选择不保存，直接切换

        # 更新侧边栏按钮状态
        self.update_sidebar_button_states("digital_human")

        # 切换到数字人页面
        self.content_stack.setCurrentIndex(1)
        self.current_content = "digital_human"

    @Slot()
    def on_voice_management_clicked(self):
        """声音管理按钮点击处理"""
        # 如果当前已经在声音管理页面，确保按钮状态正确
        if self.current_content == "voice_management":
            # 仍然需要更新按钮状态，确保UI一致性
            self.update_sidebar_button_states("voice_management")
            return

        # 如果当前在设置页面，检查是否有未保存的更改
        if self.current_content == "settings":
            # 检查设置是否已更改
            if self.settings_have_changed():
                # 弹出确认对话框
                reply = QMessageBox.question(
                    self,
                    "保存设置",
                    "设置已更改，是否保存？",
                    QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
                    QMessageBox.Save
                )

                if reply == QMessageBox.Save:
                    # 保存设置
                    self.save_settings()
                elif reply == QMessageBox.Cancel:
                    # 取消返回
                    return
                # 如果选择不保存，直接切换

        # 更新侧边栏按钮状态
        self.update_sidebar_button_states("voice_management")

        # 切换到声音管理页面
        self.content_stack.setCurrentIndex(2)
        self.current_content = "voice_management"

        # 加载声音管理数据
        self.load_vm_table_data()

    @Slot()
    def on_video_management_clicked(self):
        """视频管理按钮点击处理"""
        # 如果当前已经在视频管理页面，确保按钮状态正确
        if self.current_content == "video_management":
            # 仍然需要更新按钮状态，确保UI一致性
            self.update_sidebar_button_states("video_management")
            return

        # 如果当前在设置页面，检查是否有未保存的更改
        if self.current_content == "settings":
            # 检查设置是否已更改
            if self.settings_have_changed():
                # 弹出确认对话框
                reply = QMessageBox.question(
                    self,
                    "保存设置",
                    "设置已更改，是否保存？",
                    QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
                    QMessageBox.Save
                )

                if reply == QMessageBox.Save:
                    # 保存设置
                    self.save_settings()
                elif reply == QMessageBox.Cancel:
                    # 取消返回
                    return
                # 如果选择不保存，直接切换

        # 更新侧边栏按钮状态
        self.update_sidebar_button_states("video_management")

        # 切换到视频管理页面
        self.content_stack.setCurrentIndex(3)
        self.current_content = "video_management"

        # 加载视频管理数据
        self.load_video_management_data()

    # 视频管理功能方法
    def load_video_management_data(self):
        """加载视频管理数据"""
        try:
            if not hasattr(self, 'video_material_manager') or not self.video_material_manager:
                self.append_vm_log("❌ 视频素材管理器未初始化")
                return

            # 获取最近一周的数据
            recent_data = self.video_material_manager.get_recent_week_data()

            if recent_data.empty:
                self.vm_table.setRowCount(0)
                self.append_vm_log("📊 暂无最近一周的数据")
                return

            # 获取需要显示的列
            display_data = self.video_material_manager.get_display_columns(recent_data)

            # 填充表格
            self.populate_vm_table(display_data)
            self.append_vm_log(f"✅ 已加载 {len(display_data)} 条最近一周的数据")

        except Exception as e:
            self.append_vm_log(f"❌ 加载视频管理数据失败: {str(e)}")

    def populate_vm_table(self, df):
        """填充视频管理表格"""
        try:
            # 设置行数
            self.vm_table.setRowCount(len(df))

            # 填充数据
            for row_idx, (_, row_data) in enumerate(df.iterrows()):
                # 序号
                self.vm_table.setItem(row_idx, 0, QTableWidgetItem(str(row_idx + 1)))

                # 其他列数据
                col_idx = 1
                for col_name in ["ID", "视频URL", "上传人邮箱后缀", "拍摄演员名称",
                               "视频版型", "场景", "表现形式", "服装", "是否上传飞影", "更新日期"]:
                    if col_name in df.columns:
                        value = str(row_data[col_name]) if pd.notna(row_data[col_name]) else ""
                        self.vm_table.setItem(row_idx, col_idx, QTableWidgetItem(value))
                    else:
                        self.vm_table.setItem(row_idx, col_idx, QTableWidgetItem(""))
                    col_idx += 1

        except Exception as e:
            self.append_vm_log(f"❌ 填充表格失败: {str(e)}")

    def append_vm_log(self, message):
        """添加视频管理日志"""
        # 检查日志组件是否存在
        if not hasattr(self, 'vm_log_text') or self.vm_log_text is None:
            # 如果日志组件不存在，只打印到控制台
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"[视频管理] [{timestamp}] {message}")
            return

        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.vm_log_text.append(formatted_message)

        # 自动滚动到底部
        cursor = self.vm_log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.vm_log_text.setTextCursor(cursor)

    def toggle_vm_log(self):
        """切换视频管理日志显示/隐藏"""
        if self.vm_log_text.isVisible():
            self.vm_log_text.hide()
            self.vm_log_toggle_btn.setText("展开")
        else:
            self.vm_log_text.show()
            self.vm_log_toggle_btn.setText("收起")

    @Slot()
    def on_material_update_clicked(self):
        """素材更新按钮点击处理"""
        if not hasattr(self, 'video_material_manager') or not self.video_material_manager:
            QMessageBox.warning(self, "错误", "视频素材管理器未初始化")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认更新",
            "确定要从网站下载最新的素材数据吗？\n这个过程可能需要几分钟时间。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            self.append_vm_log("🚀 开始素材更新...")

            # 禁用按钮
            self.btn_material_update.setEnabled(False)
            self.btn_material_update.setText("更新中...")

            # 显示进度条
            self.vm_progress_bar.setVisible(True)
            self.vm_progress_bar.setRange(0, 0)  # 不确定进度

            # 启动更新线程
            from core.video_material_manager import MaterialUpdateWorker
            self.material_update_worker = MaterialUpdateWorker(self.video_material_manager)
            self.material_update_worker.finished.connect(self.on_material_update_thread_finished)
            self.material_update_worker.start()

    @Slot()
    def on_material_location_clicked(self):
        """素材位置按钮点击处理"""
        try:
            # 使用视频素材管理器的数据目录路径，确保路径正确
            if self.video_material_manager and hasattr(self.video_material_manager, 'data_dir'):
                data_dir = self.video_material_manager.data_dir
            else:
                # 备用方案：使用当前工作目录（通常是项目根目录）
                data_dir = os.path.join(os.getcwd(), "data")

            if os.path.exists(data_dir):
                import subprocess
                import platform

                if platform.system() == "Windows":
                    subprocess.run(["explorer", data_dir])
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", data_dir])
                else:  # Linux
                    subprocess.run(["xdg-open", data_dir])

                self.append_vm_log(f"📁 已打开素材位置: {data_dir}")

                # 检查avatar_list.xlsx文件是否存在
                avatar_file = os.path.join(data_dir, "avatar_list.xlsx")
                if os.path.exists(avatar_file):
                    self.append_vm_log(f"✅ 素材文件存在: avatar_list.xlsx")
                else:
                    self.append_vm_log(f"⚠️ 素材文件不存在: avatar_list.xlsx")

            else:
                self.append_vm_log(f"❌ 素材文件夹不存在: {data_dir}")
                QMessageBox.warning(self, "错误", f"素材文件夹不存在: {data_dir}")
        except Exception as e:
            self.append_vm_log(f"❌ 打开素材位置失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"打开素材位置失败: {str(e)}")



    @Slot()
    def on_material_update_thread_finished(self):
        """素材更新线程完成"""
        # 恢复按钮状态
        self.btn_material_update.setEnabled(True)
        self.btn_material_update.setText("素材更新")

        # 隐藏进度条
        self.vm_progress_bar.setVisible(False)

        # 清理工作线程
        if hasattr(self, 'material_update_worker'):
            self.material_update_worker.deleteLater()
            self.material_update_worker = None

    @Slot(bool, str)
    def on_material_update_completed(self, success, message):
        """素材更新完成处理"""
        if success:
            self.append_vm_log(f"✅ {message}")
            # 重新加载数据
            self.load_video_management_data()
            QMessageBox.information(self, "成功", message)
        else:
            self.append_vm_log(f"❌ {message}")
            QMessageBox.warning(self, "失败", message)

    @Slot(int)
    def update_vm_progress(self, progress):
        """更新视频管理进度条"""
        if hasattr(self, 'vm_progress_bar') and self.vm_progress_bar.isVisible():
            if self.vm_progress_bar.maximum() == 0:
                # 如果是不确定进度，设置为确定进度
                self.vm_progress_bar.setRange(0, 100)
            self.vm_progress_bar.setValue(progress)

    @Slot()
    def on_feying_upload_clicked(self):
        """飞影上传按钮点击处理"""
        try:
            # 检查飞影Token是否配置
            hifly_token = self.config_manager.get("hifly_token", "")
            if not hifly_token:
                QMessageBox.warning(
                    self,
                    "配置错误",
                    "请先在设置中配置飞影API Token\n\n可在飞影个人中心->API明细获取Token"
                )
                return

            # 检查数据文件是否存在
            if not self.hifly_upload_manager:
                QMessageBox.warning(
                    self,
                    "初始化错误",
                    "飞影上传管理器未初始化"
                )
                return

            avatar_list_path = self.hifly_upload_manager.avatar_list_path
            if not os.path.exists(avatar_list_path):
                QMessageBox.warning(
                    self,
                    "文件不存在",
                    f"数据文件不存在: {avatar_list_path}\n\n请确保文件存在且包含正确的数据格式"
                )
                return

            # 确认对话框
            reply = QMessageBox.question(
                self,
                "确认上传",
                "确定要开始飞影上传吗？\n\n将会上传所有标记为未上传的素材到飞影数字人平台。\n这个过程可能需要较长时间。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 开始上传
            self.start_hifly_upload()

        except Exception as e:
            self.append_vm_log(f"❌ 飞影上传启动失败: {str(e)}")
            QMessageBox.critical(
                self,
                "启动失败",
                f"飞影上传启动失败：\n\n{str(e)}"
            )

    def start_hifly_upload(self):
        """开始飞影上传"""
        try:
            if not self.hifly_upload_manager:
                self.append_vm_log("❌ 飞影上传管理器未初始化")
                return

            # 禁用上传按钮
            self.btn_feying_upload.setEnabled(False)
            self.btn_feying_upload.setText("上传中...")

            # 显示进度条
            self.vm_progress_bar.setVisible(True)
            self.vm_progress_bar.setRange(0, 100)
            self.vm_progress_bar.setValue(0)

            # 开始上传
            self.append_vm_log("🚀 开始飞影上传流程...")
            success = self.hifly_upload_manager.start_upload()

            if not success:
                # 如果启动失败，恢复按钮状态
                self.btn_feying_upload.setEnabled(True)
                self.btn_feying_upload.setText("飞影上传")
                self.vm_progress_bar.setVisible(False)

        except Exception as e:
            self.append_vm_log(f"❌ 启动飞影上传异常: {str(e)}")
            # 恢复按钮状态
            self.btn_feying_upload.setEnabled(True)
            self.btn_feying_upload.setText("飞影上传")
            self.vm_progress_bar.setVisible(False)

    @Slot(int, int, str)
    def update_hifly_upload_progress(self, current: int, total: int, task_name: str):
        """更新飞影上传进度"""
        try:
            if total > 0:
                progress = int((current / total) * 100)
                self.vm_progress_bar.setValue(progress)

                # 更新按钮文本显示进度
                self.btn_feying_upload.setText(f"上传中 {current}/{total}")

                self.append_vm_log(f"📤 [{current}/{total}] {task_name}")

        except Exception as e:
            self.append_vm_log(f"❌ 更新上传进度异常: {str(e)}")

    @Slot(bool, str)
    def on_hifly_upload_completed(self, success: bool, message: str):
        """飞影上传完成处理"""
        try:
            # 恢复按钮状态
            self.btn_feying_upload.setEnabled(True)
            self.btn_feying_upload.setText("飞影上传")
            self.vm_progress_bar.setVisible(False)

            if success:
                self.append_vm_log(f"🎉 飞影上传完成: {message}")
                QMessageBox.information(
                    self,
                    "上传完成",
                    f"飞影上传完成！\n\n{message}"
                )

                # 刷新视频管理表格数据
                self.load_video_management_data()

            else:
                self.append_vm_log(f"💔 飞影上传失败: {message}")
                QMessageBox.warning(
                    self,
                    "上传失败",
                    f"飞影上传失败：\n\n{message}"
                )

        except Exception as e:
            self.append_vm_log(f"❌ 处理上传完成事件异常: {str(e)}")

    # 视频管理搜索功能
    @Slot(str)
    def on_vm_search_text_changed(self, text):
        """视频管理搜索文本变化时自动搜索"""
        if len(text) >= 2:  # 至少输入2个字符才开始搜索
            self.vm_search_table()
        elif len(text) == 0:  # 清空搜索框时显示所有行
            self.clear_vm_search()

    @Slot()
    def on_vm_search_clicked(self):
        """视频管理搜索按钮点击处理"""
        self.vm_search_table()

    def vm_search_table(self, next=False, prev=False):
        """在视频管理表格中搜索内容"""
        search_text = self.vm_search_box.text().strip().lower()
        if not search_text:
            self.clear_vm_search()
            return

        # 如果不是查找下一个或上一个，初始化搜索变量
        if not next and not prev:
            self.vm_search_results = []
            self.vm_current_search_index = -1

            # 清除之前的高亮
            self.clear_vm_search_highlights()

            # 查找所有匹配项
            for row in range(self.vm_table.rowCount()):
                for col in range(self.vm_table.columnCount()):
                    item = self.vm_table.item(row, col)
                    if item and search_text in item.text().lower():
                        self.vm_search_results.append((row, col))

            # 显示搜索结果数量
            if self.vm_search_results:
                self.append_vm_log(f"搜索 '{search_text}' 找到 {len(self.vm_search_results)} 个匹配项")
            else:
                self.append_vm_log(f"搜索 '{search_text}' 未找到匹配内容")
                return

        # 没有找到结果
        if not hasattr(self, 'vm_search_results') or not self.vm_search_results:
            return

        # 处理导航
        if next:
            self.vm_current_search_index = (self.vm_current_search_index + 1) % len(self.vm_search_results)
        elif prev:
            self.vm_current_search_index = (self.vm_current_search_index - 1) % len(self.vm_search_results)
        else:
            self.vm_current_search_index = 0

        # 高亮当前匹配项
        self.highlight_vm_search_result()

    def highlight_vm_search_result(self):
        """高亮当前搜索结果"""
        if not hasattr(self, 'vm_search_results') or not self.vm_search_results:
            return

        # 清除之前的高亮
        self.clear_vm_search_highlights()

        # 获取当前匹配项
        row, col = self.vm_search_results[self.vm_current_search_index]

        # 高亮当前项
        item = self.vm_table.item(row, col)
        if item:
            item.setBackground(QColor("#ffeb3b"))  # 黄色高亮

        # 滚动到当前项
        self.vm_table.scrollToItem(item)

        # 更新状态信息
        current_pos = self.vm_current_search_index + 1
        total_count = len(self.vm_search_results)
        self.append_vm_log(f"搜索结果 {current_pos}/{total_count}")

    def clear_vm_search_highlights(self):
        """清除视频管理搜索高亮"""
        for row in range(self.vm_table.rowCount()):
            for col in range(self.vm_table.columnCount()):
                item = self.vm_table.item(row, col)
                if item:
                    item.setBackground(QColor())  # 清除背景色

    def clear_vm_search(self):
        """清除视频管理搜索"""
        self.vm_search_box.clear()
        # 清除高亮
        self.clear_vm_search_highlights()
        # 清除搜索结果
        if hasattr(self, 'vm_search_results'):
            self.vm_search_results = []
        self.append_vm_log("已清除搜索过滤")

    def get_current_settings_state(self):
        """获取当前设置状态的快照，用于检测更改"""
        settings = {
            "api_key": self.txt_api_key.text(),
            "api_endpoint": self.txt_api_endpoint.text(),
            "model": self.cmb_model.currentText(),
            "voice_id_excel": self.txt_voice_id_excel.text(),
            "output_dir": self.txt_output_dir.text(),
            "use_online_sheet": self.chk_use_online_sheet.isChecked(),
            "baidu_sheet_url": self.txt_baidu_sheet_url.text(),
            "use_baidu_api": self.chk_use_api.isChecked(),
            "baidu_sheets_token": self.txt_baidu_sheets_token.text(),
            "max_concurrent_tasks": self.spn_concurrent.value(),
            "audio_format": self.cmb_audio_format.currentText(),
            "mp3_bitrate": self.spn_mp3_bitrate.value(),
            "chunk_length": self.spn_chunk_length.value(),
            "normalize_audio": self.chk_normalize.isChecked(),
            "latency": self.cmb_latency.currentText(),
            "enable_clipboard_monitoring": self.chk_clipboard.isChecked(),
            "enable_fuzzy_actor_matching": self.chk_fuzzy_match.isChecked(),
            "dark_mode": self.chk_dark_mode.isChecked(),
            "enable_text_conversion": self.chk_text_conversion.isChecked(),
            "convert_numbers": self.chk_convert_numbers.isChecked(),
            "headless_mode": self.chk_headless_mode.isChecked(),
            "video_download_concurrent_browsers": self.spn_video_download_concurrent.value(),
            "video_download_search_days": self.spn_video_search_days.value(),
            "video_download_enable_screenshots": self.chk_video_enable_screenshots.isChecked()
        }
        return settings
    
    def settings_have_changed(self):
        """检查设置是否已更改"""
        if not hasattr(self, 'initial_settings'):
            return False
            
        current_settings = self.get_current_settings_state()
        
        # 比较当前设置和初始设置
        for key, value in self.initial_settings.items():
            if current_settings.get(key) != value:
                return True
                
        return False
    
    def browse_voice_id_file(self):
        """浏览声音ID列表文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择声音ID列表文件",
            "",
            "Excel文件 (*.xlsx *.xls)"
        )
        
        if file_path:
            self.txt_voice_id_excel.setText(file_path)
    
    def browse_output_dir(self):
        """浏览输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择输出目录",
            ""
        )
        
        if dir_path:
            self.txt_output_dir.setText(dir_path)
    
    def load_settings(self):
        """加载设置"""
        try:
            # 加载API设置
            self.txt_api_key.setText(self.config_manager.get("api_key", ""))
            self.txt_api_endpoint.setText(self.config_manager.get("api_endpoint", "https://api.fish.audio/v1/tts"))
            
            # 设置模型选择
            model = self.config_manager.get("model", "speech-1.5")
            index = self.cmb_model.findText(model)
            if index >= 0:
                self.cmb_model.setCurrentIndex(index)
            
            # 设置并发任务数
            self.spn_concurrent.setValue(self.config_manager.get("max_concurrent_tasks", 3))
            
            # 设置音频格式
            audio_format = self.config_manager.get("audio_format", "mp3")
            index = self.cmb_audio_format.findText(audio_format)
            if index >= 0:
                self.cmb_audio_format.setCurrentIndex(index)
            
            # 设置MP3比特率
            mp3_bitrate = self.config_manager.get("mp3_bitrate", 192)
            self.spn_mp3_bitrate.setValue(mp3_bitrate)
            
            # 设置分块长度
            self.spn_chunk_length.setValue(self.config_manager.get("chunk_length", 200))
            
            # 设置归一化音频
            self.chk_normalize.setChecked(self.config_manager.get("normalize_audio", True))
            
            # 设置延迟模式
            latency = self.config_manager.get("latency", "normal")
            index = self.cmb_latency.findText(latency)
            if index >= 0:
                self.cmb_latency.setCurrentIndex(index)
            
            # 设置声音ID文件
            self.txt_voice_id_excel.setText(self.config_manager.get("voice_id_excel", ""))
            
            # 设置输出目录
            self.txt_output_dir.setText(self.config_manager.get("output_dir", "output"))
            
            # 设置百度表格选项
            self.chk_use_online_sheet.setChecked(self.config_manager.get("use_online_sheet", False))
            self.txt_baidu_sheet_url.setText(self.config_manager.get("baidu_sheet_url", ""))
            self.chk_use_api.setChecked(self.config_manager.get("use_baidu_api", True))
            self.txt_baidu_sheets_token.setText(self.config_manager.get("baidu_sheets_token", ""))
            
            # 更新百度表格相关控件的启用状态
            self.toggle_baidu_sheet_url(self.chk_use_online_sheet.isChecked())
            self.toggle_baidu_api_settings(self.chk_use_online_sheet.isChecked())
            self.toggle_baidu_api_token(self.chk_use_api.isChecked())
            
            # 设置其他选项
            self.chk_clipboard.setChecked(self.config_manager.get("enable_clipboard_monitoring", True))
            self.chk_fuzzy_match.setChecked(self.config_manager.get("enable_fuzzy_actor_matching", True))
            self.chk_dark_mode.setChecked(self.config_manager.get("dark_mode", False))
            
            # 设置文本转换选项
            self.chk_text_conversion.setChecked(self.config_manager.get("enable_text_conversion", True))
            self.chk_convert_numbers.setChecked(self.config_manager.get("convert_numbers", True))
            self.chk_convert_numbers.setEnabled(self.chk_text_conversion.isChecked())
            
            # 更新剪贴板管理器的文本转换设置
            self.clipboard_manager.set_text_conversion_settings(
                self.config_manager.get("enable_text_conversion", True),
                self.config_manager.get("convert_numbers", True)
            )
            
            return True
        except Exception as e:
            print(f"加载设置失败: {e}")
            return False
    
    def save_settings(self):
        """保存设置"""
        try:
            # 保存通用设置
            self.config_manager.set("enable_clipboard_monitoring", self.chk_clipboard.isChecked())
            self.config_manager.set("dark_mode", self.chk_dark_mode.isChecked())

            # 保存声音克隆设置 - Fish Audio API
            self.config_manager.set("api_key", self.txt_api_key.text())
            self.config_manager.set("api_endpoint", self.txt_api_endpoint.text())
            self.config_manager.set("model", self.cmb_model.currentText())

            # 保存声音克隆设置 - 声音ID设置
            self.config_manager.set("baidu_sheet_url", self.txt_baidu_sheet_url.text())
            self.config_manager.set("baidu_sheets_token", self.txt_baidu_sheets_token.text())

            # 保存声音克隆设置 - 处理设置
            try:
                concurrent_value = int(self.spn_concurrent.text())
                if 1 <= concurrent_value <= 5:
                    self.config_manager.set("max_concurrent", concurrent_value)
                else:
                    self.config_manager.set("max_concurrent", 3)  # 默认值
            except ValueError:
                self.config_manager.set("max_concurrent", 3)  # 默认值

            # 保存数字人设置
            self.config_manager.set("headless_mode", self.chk_headless_mode.isChecked())

            # 保存飞影设置
            self.config_manager.set("hifly_token", self.txt_hifly_token.text())

            # 保存视频处理方案
            video_process_mode = self.cmb_video_process_mode.currentData()
            self.config_manager.set("video_processing_mode", video_process_mode)

            # 保存视频处理并发数量
            self.config_manager.set("video_processing_concurrent", self.spn_video_processing_concurrent.value())

            # 保存API批次大小设置
            try:
                api_batch_size = int(self.spn_video_concurrent.text())
                if 1 <= api_batch_size <= 10:
                    self.config_manager.set("api_batch_size", api_batch_size)
                else:
                    self.config_manager.set("api_batch_size", 6)  # 默认值
            except ValueError:
                self.config_manager.set("api_batch_size", 6)  # 默认值

            # 保存视频下载设置
            self.config_manager.set("video_download_concurrent_browsers", self.spn_video_download_concurrent.value())
            self.config_manager.set("video_download_search_days", self.spn_video_search_days.value())
            self.config_manager.set("video_download_enable_screenshots", self.chk_video_enable_screenshots.isChecked())

            # 保存羽化参数
            self.config_manager.set("feather_radius", self.feather_radius_spin.value())
            self.config_manager.set("feather_sigma", self.feather_sigma_spin.value())
            self.config_manager.set("edge_blur_radius", self.edge_blur_spin.value())

            # 同步状态栏的剪切板开关状态
            clipboard_enabled = self.chk_clipboard.isChecked()
            self.btn_toggle_clipboard.setChecked(clipboard_enabled)
            if clipboard_enabled:
                self.btn_toggle_clipboard.setText("剪贴板监听: 开启")
                # 如果设置为启用但当前未监听，则启动监听
                if not self.clipboard_manager.is_monitoring():
                    self.clipboard_manager.start_monitoring()
            else:
                self.btn_toggle_clipboard.setText("剪贴板监听: 关闭")
                # 如果设置为禁用但当前在监听，则停止监听
                if self.clipboard_manager.is_monitoring():
                    self.clipboard_manager.stop_monitoring()

            # 保存完成，切换回主页面
            self.on_sound_clone_clicked()
            self.statusBar.showMessage("设置已保存", 3000)
            self.append_log("设置已保存")
            return True
        except Exception as e:
            print(f"保存设置失败: {e}")
            self.statusBar.showMessage(f"保存设置失败: {str(e)}", 5000)
            return False
    
    def cancel_settings(self):
        """取消设置并返回主界面"""
        # 重新加载设置以撤销更改
        self.load_settings_panel()

        # 切换回声音克隆页面
        self.on_sound_clone_clicked()

    def auto_save_settings(self):
        """自动保存设置（不显示消息）"""
        try:
            # 通用设置
            self.config_manager.set("enable_clipboard_monitoring", self.chk_clipboard.isChecked())
            self.config_manager.set("dark_mode", self.chk_dark_mode.isChecked())

            # 声音克隆设置 - API设置
            self.config_manager.set("api_key", self.txt_api_key.text())
            self.config_manager.set("api_endpoint", self.txt_api_endpoint.text())
            model = self.cmb_model.currentText()
            self.config_manager.set("model", model)

            # 声音克隆设置 - 声音ID设置
            self.config_manager.set("baidu_sheet_url", self.txt_baidu_sheet_url.text())
            self.config_manager.set("baidu_sheets_token", self.txt_baidu_sheets_token.text())

            # 声音克隆设置 - 处理设置
            try:
                concurrent = int(self.spn_concurrent.text())
                if 1 <= concurrent <= 5:
                    self.config_manager.set("max_concurrent", concurrent)
            except ValueError:
                pass  # 忽略无效输入

            # 数字人设置
            self.config_manager.set("headless_mode", self.chk_headless_mode.isChecked())

            # 飞影设置
            self.config_manager.set("hifly_token", self.txt_hifly_token.text())

            # 保存视频下载设置
            self.config_manager.set("video_download_concurrent_browsers", self.spn_video_download_concurrent.value())
            self.config_manager.set("video_download_search_days", self.spn_video_search_days.value())
            self.config_manager.set("video_download_enable_screenshots", self.chk_video_enable_screenshots.isChecked())

            # 保存API批次大小设置
            try:
                api_batch_size = int(self.spn_video_concurrent.text())
                if 1 <= api_batch_size <= 10:
                    self.config_manager.set("api_batch_size", api_batch_size)
            except ValueError:
                pass  # 忽略无效输入

            # 保存配置
            self.config_manager.save()

        except Exception as e:
            # 静默处理错误，不显示消息
            print(f"自动保存设置时出错: {e}")

    def reset_settings(self):
        """重置为默认设置"""
        # 确认重置
        reply = QMessageBox.question(
            self,
            "重置设置",
            "确定要将所有设置重置为默认值吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 重置配置管理器
            self.config_manager.reset_to_defaults()
            
            # 重新加载设置
            self.load_settings()
            
            # 显示消息
            self.statusBar.showMessage("设置已重置为默认值", 3000)
    
    @Slot()
    def on_open_output_clicked(self):
        """打开输出文件夹按钮点击处理"""
        # 使用配置管理器获取要打开的文件夹
        folder = self.config_manager.get_output_folder_for_open()

        # 确保文件夹存在
        if not os.path.exists(folder):
            try:
                os.makedirs(folder, exist_ok=True)
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "错误",
                    f"无法创建输出文件夹：{str(e)}"
                )
                return
        
        # 打开文件夹
        try:
            if sys.platform == 'win32':
                os.startfile(folder)
            elif sys.platform == 'darwin':  # macOS
                import subprocess
                subprocess.Popen(['open', folder])
            else:  # Linux
                import subprocess
                subprocess.Popen(['xdg-open', folder])
        except Exception as e:
            QMessageBox.critical(
                self,
                "错误",
                f"无法打开输出文件夹：{str(e)}"
            )
    
    @Slot()
    def on_start_process_clicked(self):
        """开始处理按钮点击处理"""
        # 检查是否已经在运行
        if self.processor.is_running:
            QMessageBox.warning(
                self,
                "已在处理中",
                "已经有一个处理任务在运行，请等待其完成。"
            )
            return
        
        # 先从表格更新处理器数据
        self.update_processor_data_from_table()
        
        # 检查是否有数据
        if self.processor.scripts_df is None or self.processor.scripts_df.empty:
            QMessageBox.warning(
                self,
                "无数据",
                "没有数据可处理，请先加载或粘贴数据。"
            )
            return
        
        # 移除处理器数据中的空行
        if not self.processor.scripts_df.empty:
            # 修复检测空行的代码，避免使用isna()方法在字符串上
            empty_rows = self.processor.scripts_df.apply(
                lambda row: (
                    pd.isna(row).all() or 
                    row.astype(str).str.strip().eq('').all() or
                    pd.isna(row.get("克隆文案", "")) or str(row.get("克隆文案", "")).strip() == "" or
                    pd.isna(row.get("演员形象", "")) or str(row.get("演员形象", "")).strip() == ""
                ), 
                axis=1
            )
            
            if empty_rows.any():
                # 移除空行并重置索引
                self.processor.scripts_df = self.processor.scripts_df.loc[~empty_rows].reset_index(drop=True)
                print(f"移除了 {empty_rows.sum()} 个空行，剩余 {len(self.processor.scripts_df)} 行")
                # 更新表格显示
                self.update_table_from_dataframe(self.processor.scripts_df, append=False)
        
        # 检查是否还有剩余数据
        if self.processor.scripts_df.empty:
            QMessageBox.warning(
                self,
                "无有效数据",
                "处理后没有有效数据可处理，请检查数据格式是否正确。"
            )
            return
        
        # 检查声音ID数据是否已加载
        if not self.processor.voice_id_map:
            try:
                # 检查是否使用在线表格
                use_online = self.config_manager.get("use_online_sheet", False)
                if use_online:
                    baidu_sheet_url = self.config_manager.get("baidu_sheet_url", "")
                    if not baidu_sheet_url:
                        QMessageBox.warning(
                            self,
                            "百度表格URL未设置",
                            "已启用在线表格功能，但未设置百度表格URL。请在设置中配置正确的URL。"
                        )
                        return
                    
                    # 检查是否使用API方式
                    use_api = self.config_manager.get("use_baidu_api", False)
                    api_method_text = "API" if use_api else "网页抓取"
                    
                    self.append_log(f"正在使用{api_method_text}方式从百度在线表格加载声音ID数据...")
                    
                    # 使用异步方法但同步调用
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        self.append_log("警告: 事件循环已在运行，将使用本地Excel")
                        use_online = False
                    else:
                        try:
                            if use_api:
                                api_token = self.config_manager.get("baidu_sheets_token", "")
                                if not api_token:
                                    self.append_log("警告: 已启用API模式，但未设置访问令牌，将使用网页抓取模式")
                                    self.processor.voice_id_map = loop.run_until_complete(
                                        self.processor.load_voice_ids_from_baidu_sheet(baidu_sheet_url)
                                    )
                                else:
                                    self.processor.voice_id_map = loop.run_until_complete(
                                        self.processor.load_voice_ids_from_baidu_sheet_api(baidu_sheet_url)
                                    )
                            else:
                                self.processor.voice_id_map = loop.run_until_complete(
                                    self.processor.load_voice_ids_from_baidu_sheet(baidu_sheet_url)
                                )
                        except Exception as e:
                            self.append_log(f"从在线表格加载声音ID失败: {str(e)}")
                            use_online = False
                
                # 如果未使用在线表格或在线加载失败，使用本地Excel
                if not use_online or not self.processor.voice_id_map:
                    voice_id_file = self.config_manager.get("voice_id_excel", config.VOICE_ID_EXCEL)
                    self.append_log(f"从本地Excel加载声音ID数据: {voice_id_file}")
                    self.processor.load_voice_ids(voice_id_file, use_online=False)
                
                if not self.processor.voice_id_map:
                    data_source = "百度在线表格" if use_online else f"本地文件 {self.config_manager.get('voice_id_excel', config.VOICE_ID_EXCEL)}"
                    QMessageBox.warning(
                        self,
                        "加载声音ID失败",
                        f"无法从{data_source}加载声音ID数据。请检查数据源是否可用和格式是否正确。"
                    )
                    return
                else:
                    self.append_log(f"成功加载 {len(self.processor.voice_id_map)} 个声音ID")
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "加载声音ID错误",
                    f"加载声音ID时发生错误: {str(e)}"
                )
                return
        
        # 确认是否有未完成的项目
        unfinished_count = len(self.processor.scripts_df[self.processor.scripts_df[config.STATUS_COLUMN] != config.COMPLETED_STATUS])
        
        if unfinished_count == 0:
            QMessageBox.information(
                self,
                "全部完成",
                "所有项目已经处理完成。"
            )
            return
            
        # 获取或创建配音文件夹（当天只使用一个配音文件夹）
        download_folder = self.config_manager.get_or_create_download_folder()

        self.append_log(f"使用配音文件夹: {os.path.basename(download_folder)}")

        # 设置处理器的下载文件夹
        self.processor.download_dir = download_folder
        
        # 开始处理
        self.btn_start_process.setEnabled(False)
        self.btn_start_process.setText("处理中...")
        self.progress_bar.setValue(0)
        
        # 清空日志
        self.txt_log.clear()
        
        # 开始处理
        self.processor.start_processing()
        
        # 更新状态
        self.lbl_status.setText("处理中...")
    
    @Slot(pd.DataFrame)
    def on_clipboard_data_detected(self, df):
        """剪贴板数据检测到信号处理"""
        if df is not None and not df.empty:
            # 打印检测到的列名
            print(f"剪贴板数据检测: 包含{len(df)}行，{len(df.columns)}列")
            print(f"列名: {list(df.columns)}")
            
            # 打印脚本文本列是否存在
            if config.SCRIPT_TEXT_COLUMN in df.columns:
                print(f"包含克隆文案列，数据示例: {df[config.SCRIPT_TEXT_COLUMN].iloc[0][:50]}..." if len(df) > 0 and len(str(df[config.SCRIPT_TEXT_COLUMN].iloc[0])) > 50 else f"包含克隆文案列")
            else:
                print(f"警告: 未找到克隆文案列！")
            
            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                "检测到数据",
                f"剪贴板中检测到 {len(df)} 条数据，是否导入？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                # 先创建工作文件夹（如果不存在）
                try:
                    work_folders = self.config_manager.create_daily_work_folder()
                    self.append_log(f"准备今日工作环境: {work_folders['main_folder']}")
                except Exception as e:
                    self.append_log(f"创建工作文件夹失败: {e}")

                # 过滤数据，只保留必要的列
                filtered_df = self.filter_dataframe_columns(df)
                self.append_log(f"过滤后的数据: {len(filtered_df)}行，{len(filtered_df.columns)}列")

                # 是否追加数据
                if self.table_scripts.rowCount() > 0:
                    reply = QMessageBox.question(
                        self,
                        "粘贴数据",
                        "是否要将数据追加到现有数据后？\n选择\"否\"将覆盖现有数据。",
                        QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                        QMessageBox.Yes
                    )

                    if reply == QMessageBox.Cancel:
                        return

                    append = (reply == QMessageBox.Yes)
                else:
                    append = False

                # 保存当前状态到历史记录
                self.save_current_state()

                # 更新表格（使用过滤后的数据）
                self.update_table_from_dataframe(filtered_df, append=append)

                # 更新处理器数据（使用过滤后的数据）
                if append and self.processor.scripts_df is not None:
                    self.processor.scripts_df = pd.concat([self.processor.scripts_df, filtered_df], ignore_index=True)
                else:
                    self.processor.scripts_df = filtered_df
                
                # 自动保存到文件
                self.auto_save_to_daily_excel()
                
                # 显示消息
                self.statusBar.showMessage(f"已{'追加' if append else '粘贴'} {len(df)} 条文案", 3000)
    
    @Slot(int, int, str)
    def update_progress(self, current, total, status):
        """更新进度条"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
        self.lbl_status.setText(status)
    
    @Slot(int, str, str)
    def update_item_status(self, index, status, time):
        """更新项目状态"""
        # 如果index超出了表格范围，不处理
        if index >= self.table_scripts.rowCount():
            return

        # 暂时断开表格项变更信号，避免在处理过程中触发重复的数据更新
        self.table_scripts.itemChanged.disconnect()

        try:
            # 更新状态单元格(索引7)和生成时间单元格(索引8)
            self.table_scripts.item(index, 7).setText(status)
            if time:
                self.table_scripts.item(index, 8).setText(time)

            # 更新状态单元格颜色
            self.update_status_cell_color(index, status)

            # 刷新表格显示
            self.table_scripts.viewport().update()
        finally:
            # 重新连接信号
            self.table_scripts.itemChanged.connect(self.on_table_item_changed)
    
    @Slot(bool, str)
    def on_process_finished(self, success, message):
        """处理完成信号处理"""
        # 启用开始处理按钮
        self.btn_start_process.setEnabled(True)
        self.btn_start_process.setText("开始处理")
        
        # 更新状态
        self.lbl_status.setText("处理完成")
        
        # 显示消息
        if success:
            QMessageBox.information(
                self,
                "处理完成",
                message
            )
        else:
            QMessageBox.warning(
                self,
                "处理中断",
                message
            )
        
        # 重新加载数据
        if self.processor.scripts_df is not None:
            self.update_table_from_dataframe(self.processor.scripts_df, append=False)
    
    def append_log(self, message):
        """追加日志消息"""
        # 添加时间戳
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 在文本末尾追加消息
        self.txt_log.append(f"[{timestamp}] {message}")

        # 更新日志计数
        self.log_count += 1
        self.update_log_count()

        # 打印到控制台
        print(f"[{timestamp}] {message}")

        # 滚动到底部
        scrollbar = self.txt_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def update_table_from_dataframe(self, df, append=False):
        """从DataFrame更新表格"""
        if df is None or df.empty:
            return
            
        # 保存当前滚动位置
        scrollbar = self.table_scripts.verticalScrollBar()
        scroll_pos = scrollbar.value()
        
        # 开始更新前阻止表格信号
        self.table_scripts.blockSignals(True)
        
        # 如果不是追加模式，清空表格
        if not append:
            self.table_scripts.setRowCount(0)
        
        current_row_count = self.table_scripts.rowCount()
        
        # 获取表格的预期列顺序
        expected_columns = self.get_expected_columns()
        
        # 添加新行
        for i, row in df.iterrows():
            row_index = current_row_count + i
            self.table_scripts.insertRow(row_index)
            
            # 设置固定行高
            self.table_scripts.setRowHeight(row_index, self.default_row_height)
            
            # 按预期列顺序填充数据
            for col_idx, col_name in enumerate(expected_columns):
                if col_idx < self.table_scripts.columnCount() - 1:  # -1是因为最后一列是操作列
                    cell_value = ""

                    # 序号列特殊处理，显示行号
                    if col_name == '序号':
                        cell_value = str(row_index + 1)  # 行号从1开始
                    elif col_name in df.columns:
                        # 确保nan和None值转换为空字符串
                        value = row[col_name]
                        if isinstance(value, pd.Series):
                            # 如果值是Series（可能是因为列名重复），转换为字符串
                            cell_value = str(value.iloc[0]) if not pd.isna(value.iloc[0]) else ""
                        elif pd.isna(value) or value is None or str(value).lower() == 'nan':
                            cell_value = ""
                        else:
                            cell_value = str(value)

                    item = QTableWidgetItem(cell_value)
                    # 序号列设为只读
                    if col_name == '序号':
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    else:
                        item.setFlags(item.flags() | Qt.ItemIsEditable)
                    self.table_scripts.setItem(row_index, col_idx, item)
                    
                    # 对完成状态列应用颜色
                    if col_name == '完成状态':
                        self.update_status_cell_color(row_index, cell_value)
        
            # 为操作列添加空项并打开持久编辑器
            operation_item = QTableWidgetItem("")
            self.table_scripts.setItem(row_index, 10, operation_item)
            # 使用QTableWidgetItem方式打开编辑器
            self.table_scripts.openPersistentEditor(operation_item)
    
        # 恢复表格信号
        self.table_scripts.blockSignals(False)
        
        # 恢复滚动位置
        scrollbar.setValue(scroll_pos)
    
    def update_status_cell_color(self, row, status):
        """更新状态单元格的颜色"""
        # 完成状态列现在是索引7
        item = self.table_scripts.item(row, 7)  # 状态列索引为7
        if item:
            if status == "已完成":
                item.setBackground(QColor(200, 255, 200))  # 浅绿色
            elif status == "生成失败":
                item.setBackground(QColor(255, 200, 200))  # 浅红色
            elif status == "未搜到声音":
                item.setBackground(QColor(255, 255, 180))  # 浅黄色
            elif status == "处理中":
                item.setBackground(QColor(200, 200, 255))  # 浅蓝色
            else:
                item.setBackground(QColor(255, 255, 255) if not self.dark_mode else QColor(52, 58, 64))

    def show_table_context_menu(self, pos):
        """显示表格的右键菜单"""
        # 创建菜单
        menu = QMenu(self)

        # 添加"新增一行"菜单项 - 这个选项在任何情况下都应该可用
        action_add_row = QAction("新增一行", self)
        action_add_row.triggered.connect(self.add_new_row)
        menu.addAction(action_add_row)

        # 获取选中的行
        selected_rows = set()
        for index in self.table_scripts.selectedIndexes():
            selected_rows.add(index.row())

        # 检查表格是否有数据行
        has_rows = self.table_scripts.rowCount() > 0

        if has_rows and selected_rows:  # 如果表格有数据且有选中的行
            # 在选中行后添加一行
            action_add_row_after = QAction("在选中行后添加一行", self)
            action_add_row_after.triggered.connect(lambda: self.add_new_row(after_row=list(selected_rows)[-1]))
            menu.addAction(action_add_row_after)

            menu.addSeparator()

            # 添加删除菜单项
            if len(selected_rows) == 1:
                # 只选中了一行的情况
                row = list(selected_rows)[0]
                action_delete = QAction(f"删除第{row+1}行", self)
                action_delete.triggered.connect(lambda: self.on_delete_row_clicked(row))
                menu.addAction(action_delete)
            else:
                # 选中了多行的情况
                action_delete_selected = QAction(f"删除选中的 {len(selected_rows)} 行", self)
                action_delete_selected.triggered.connect(lambda: self.on_delete_selected_rows())
                menu.addAction(action_delete_selected)

            action_delete_all = QAction("删除全部", self)
            action_delete_all.triggered.connect(self.on_delete_all_rows_clicked)
            menu.addAction(action_delete_all)
        elif has_rows and not selected_rows:
            # 表格有数据但没有选中行的情况，仍然提供删除全部选项
            menu.addSeparator()
            action_delete_all = QAction("删除全部", self)
            action_delete_all.triggered.connect(self.on_delete_all_rows_clicked)
            menu.addAction(action_delete_all)

        # 显示菜单
        menu.exec_(QCursor.pos())
        
    def add_new_row(self, after_row=None):
        """添加新的空白行

        Args:
            after_row: 如果指定，则在此行之后添加新行；否则在表格末尾添加
        """
        try:
            # 保存当前状态到历史记录
            self.save_current_state()

            # 确定插入位置
            if after_row is not None and after_row is not False and isinstance(after_row, int):
                insert_position = after_row + 1
            else:
                insert_position = self.table_scripts.rowCount()

            # 插入新行
            self.table_scripts.insertRow(insert_position)

            # 添加空单元格
            expected_columns = self.get_expected_columns()
            for col in range(10):  # 现在有10个数据列
                if col < len(expected_columns):
                    col_name = expected_columns[col]
                    if col_name == '序号':
                        # 序号列显示行号，设为只读
                        item = QTableWidgetItem(str(insert_position + 1))
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    else:
                        item = QTableWidgetItem("")
                        item.setFlags(item.flags() | Qt.ItemIsEditable)
                    self.table_scripts.setItem(insert_position, col, item)
                else:
                    self.table_scripts.setItem(insert_position, col, QTableWidgetItem(""))
            
            # 为操作列创建编辑器
            operation_item = QTableWidgetItem("")
            self.table_scripts.setItem(insert_position, 10, operation_item)

            # 使用QTableWidgetItem方式打开持久编辑器
            try:
                # 确保先设置了代理
                if hasattr(self, 'button_delegate'):
                    # 使用QTableWidgetItem方式打开编辑器
                    self.table_scripts.openPersistentEditor(operation_item)
                else:
                    # 如果代理未初始化，直接设置cellWidget作为备用方案
                    self._create_operation_cell_widget(insert_position)
            except Exception as editor_error:
                # 如果打开持久编辑器失败，使用备用方案
                self._create_operation_cell_widget(insert_position)
            
            # 设置行高
            self.table_scripts.setRowHeight(insert_position, self.default_row_height)
            
            # 选中新行
            self.table_scripts.selectRow(insert_position)
            
            # 更新处理器数据
            self.update_processor_data_from_table()

            # 更新所有行的序号
            self.update_row_numbers()

            # 刷新操作列编辑器
            self.refresh_operation_column_editors()

            # 自动保存更改
            self.auto_save_to_daily_excel()

            self.statusBar.showMessage(f"已添加新行", 3000)
        except Exception as e:
            print(f"添加新行出错: {e}")
            import traceback
            traceback.print_exc()

    def _create_operation_cell_widget(self, row):
        """为指定行创建操作列的cell widget（备用方案）"""
        try:
            from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton
            widget = QWidget()
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(2, 2, 2, 2)
            layout.setSpacing(2)

            btn_delete = QPushButton("删除")
            btn_delete.setObjectName("deleteButton")
            btn_delete.setToolTip("删除该行")
            btn_delete.setFixedHeight(24)
            btn_delete.setFixedWidth(42)
            btn_delete.setStyleSheet("font-size: 11px; padding: 2px 0; font-family: 'Microsoft YaHei UI', 'Segoe UI', sans-serif;")
            btn_delete.clicked.connect(lambda: self.on_delete_row_clicked(row))

            layout.setAlignment(Qt.AlignCenter)
            layout.addWidget(btn_delete)
            widget.setLayout(layout)

            self.table_scripts.setCellWidget(row, 10, widget)
        except Exception as e:
            print(f"创建操作列widget失败: {e}")

    def update_row_numbers(self):
        """更新所有行的序号"""
        try:
            expected_columns = self.get_expected_columns()
            if '序号' in expected_columns:
                seq_col_idx = expected_columns.index('序号')
                for row in range(self.table_scripts.rowCount()):
                    item = QTableWidgetItem(str(row + 1))
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # 设为只读
                    self.table_scripts.setItem(row, seq_col_idx, item)
        except Exception as e:
            print(f"更新行号出错: {e}")

    def refresh_operation_column_editors(self):
        """刷新操作列的编辑器"""
        try:
            # 重新为所有行的操作列设置编辑器
            for row in range(self.table_scripts.rowCount()):
                operation_item = self.table_scripts.item(row, 10)
                if operation_item is None:
                    operation_item = QTableWidgetItem("")
                    self.table_scripts.setItem(row, 10, operation_item)

                # 关闭旧的编辑器
                self.table_scripts.closePersistentEditor(operation_item)
                # 重新打开编辑器
                self.table_scripts.openPersistentEditor(operation_item)
        except Exception as e:
            print(f"刷新操作列编辑器出错: {e}")

    def on_delete_selected_rows(self):
        """删除所有选中的行"""
        try:
            # 获取选中的行并排序（降序，从后向前删除避免索引变化）
            selected_rows = sorted([index.row() for index in self.table_scripts.selectedIndexes()], reverse=True)
            # 去重
            selected_rows = sorted(set(selected_rows), reverse=True)
            
            if not selected_rows:
                return
            
            # 保存当前状态到历史记录
            self.save_current_state()
                
            self.statusBar.showMessage(f"删除 {len(selected_rows)} 行", 3000)
            
            # 从后向前删除行，避免索引变化
            for row in selected_rows:
                self.table_scripts.removeRow(row)
            
            # 更新处理器的数据
            self.update_processor_data_from_table()
            
            # 如果表格现在为空，清空处理器数据但不自动添加空行
            if self.table_scripts.rowCount() == 0:
                # 清空处理器数据
                self.processor.scripts_df = pd.DataFrame(columns=self.get_expected_columns())
            
            # 自动保存更改
            self.auto_save_to_daily_excel()
            
            # 显示消息
            self.append_log(f"已删除 {len(selected_rows)} 行数据")
        except Exception as e:
            print(f"删除行出错: {e}")
            import traceback
            traceback.print_exc()
            
    def on_delete_row_clicked(self, row):
        """删除行按钮点击处理"""
        # 确认删除
        try:
            # 避免使用QMessageBox.question，因为它会触发paint事件导致无限循环
            reply = QMessageBox.StandardButton.Yes
            self.statusBar.showMessage(f"删除第{row+1}行", 3000)
            
            # 保存当前状态到历史记录
            self.save_current_state()
            
            # 删除行
            self.table_scripts.removeRow(row)
            
            # 更新处理器的数据
            self.update_processor_data_from_table()
            
            # 如果表格现在为空，清空处理器数据但不自动添加空行
            if self.table_scripts.rowCount() == 0:
                # 清空处理器数据
                self.processor.scripts_df = pd.DataFrame(columns=self.get_expected_columns())
            
            # 更新所有行的序号
            self.update_row_numbers()

            # 刷新操作列编辑器
            self.refresh_operation_column_editors()

            # 自动保存更改
            self.auto_save_to_daily_excel()
        except Exception as e:
            print(f"删除行出错: {e}")
            import traceback
            traceback.print_exc()
            
    def on_delete_all_rows_clicked(self):
        """删除所有行按钮点击处理"""
        # 确认删除
        try:
            # 避免使用QMessageBox.question，因为它会触发paint事件导致无限循环
            reply = QMessageBox.StandardButton.Yes
            self.statusBar.showMessage("删除所有数据", 3000)
            
            # 保存当前状态到历史记录
            self.save_current_state()
            
            # 删除所有行
            self.table_scripts.setRowCount(0)
            
            # 清空处理器的数据
            self.processor.scripts_df = pd.DataFrame(columns=self.get_expected_columns())
            print("清空所有数据")
            
            # 自动保存更改
            self.auto_save_to_daily_excel()
        except Exception as e:
            print(f"删除所有行出错: {e}")
            import traceback
            traceback.print_exc()
    
    @Slot()
    def on_save_data_clicked(self):
        """保存数据按钮点击处理"""
        # 先从表格更新处理器数据
        self.update_processor_data_from_table()
        
        if self.processor.scripts_df is None or self.processor.scripts_df.empty:
            QMessageBox.warning(
                self,
                "保存数据",
                "没有数据可保存。"
            )
            return
        
        # 打开文件对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存文案Excel文件",
            "",
            "Excel文件 (*.xlsx)"
        )
        
        if file_path:
            # 确保文件名以.xlsx结尾
            if not file_path.endswith('.xlsx'):
                file_path += '.xlsx'
            
            # 保存文件
            try:
                self.processor.save_scripts(file_path)
                # 显示消息
                self.statusBar.showMessage(f"已保存 {len(self.processor.scripts_df)} 条文案到 {file_path}", 3000)
            except Exception as e:
                # 显示错误消息
                QMessageBox.critical(
                    self,
                    "保存失败",
                    f"无法保存文案Excel文件：{str(e)}"
                ) 

    @Slot(QTableWidgetItem)
    def on_table_item_changed(self, item):
        """表格项变更时触发自动保存"""
        # 忽略操作列的变更
        if item.column() == 9:  # 操作列
            return

        # 如果不是在撤销操作中，则保存历史状态
        if not self.is_undoing:
            self.save_current_state()

        # 更新处理器数据
        self.update_processor_data_from_table()

        # 使用防抖机制避免频繁保存
        if not self.processor.is_running:
            # 如果已有定时器在运行，重置它
            if hasattr(self, '_save_timer') and self._save_timer.isActive():
                self._save_timer.stop()

            # 创建新的定时器，延迟500ms保存
            if not hasattr(self, '_save_timer'):
                self._save_timer = QTimer()
                self._save_timer.setSingleShot(True)
                self._save_timer.timeout.connect(self.auto_save_to_daily_excel)

            self._save_timer.start(500)  # 500ms延迟
        
        # 注释掉行高调整相关代码
        # 不再根据文本内容调整行高，保持原有行高不变
    
    def auto_save_to_daily_excel(self):
        """自动保存表格数据到Excel"""
        try:
            # 如果处理器数据为None，创建空的DataFrame
            if self.processor.scripts_df is None:
                self.processor.scripts_df = pd.DataFrame(columns=self.get_expected_columns())

            # 修改这部分逻辑，保存到当前加载的文件，而不是默认今日文件
            if hasattr(self.processor, 'current_loaded_file') and self.processor.current_loaded_file:
                save_path = self.processor.current_loaded_file
                self.append_log(f"保存到当前加载的文件: {save_path}")
            else:
                # 如果没有当前加载的文件，使用配置管理器创建正确的路径
                try:
                    work_folders = self.config_manager.create_daily_work_folder()
                    today = datetime.now().strftime('%Y%m%d')
                    save_path = os.path.join(work_folders['main_folder'], f"文案_{today}.xlsx")
                    self.append_log(f"自动创建今日工作文件: {save_path}")
                except Exception as e:
                    self.append_log(f"创建工作文件夹失败: {e}")
                    return

            # 保存到指定路径 - save_scripts方法会自动输出日志
            self.processor.save_scripts(save_path)
        except Exception as e:
            self.append_log(f"自动保存失败: {str(e)}")

    @Slot()
    def on_refresh_data_clicked(self):
        """刷新声音ID数据按钮点击处理"""
        # 显示刷新状态
        self.statusBar.showMessage("正在刷新声音ID数据...", 5000)
        self.btn_refresh.setEnabled(False)
        self.btn_refresh.setText("刷新中...")

        # 使用QTimer保证UI先更新
        QTimer.singleShot(100, self.perform_voice_id_refresh)
    
    def perform_voice_id_refresh(self):
        """执行声音ID数据刷新（专门用于刷新声音按钮）"""
        try:
            # 清空当前声音ID数据，确保重新加载
            self.processor.voice_id_map = {}

            # 检查在线模式配置
            baidu_sheet_url = self.config_manager.get("baidu_sheet_url", "")
            baidu_sheets_token = self.config_manager.get("baidu_sheets_token", "")

            if not baidu_sheet_url or not baidu_sheets_token:
                # 恢复按钮状态
                self.btn_refresh.setEnabled(True)
                self.btn_refresh.setText("刷新声音")
                QMessageBox.warning(
                    self,
                    "配置不完整",
                    "刷新失败，请在设置中输入知识库链接及API。\n\n请确保已正确配置：\n• 知识库URL\n• 百度表格API密钥"
                )
                self.append_log("错误: 刷新声音ID需要配置知识库URL和API密钥")
                return

            # 强制启用在线模式进行刷新
            self.append_log("开始从百度知识库刷新声音ID数据...")

            # 临时设置在线模式
            original_use_online = self.config_manager.get("use_online_sheet", False)
            self.config_manager.set("use_online_sheet", True)

            try:
                # 刷新声音ID数据（强制在线模式）
                self.refresh_voice_id_data(force_update=True)
            finally:
                # 恢复原始设置
                self.config_manager.set("use_online_sheet", original_use_online)

            # 恢复按钮状态
            self.btn_refresh.setEnabled(True)
            self.btn_refresh.setText("刷新声音")

        except Exception as e:
            # 恢复按钮状态
            self.btn_refresh.setEnabled(True)
            self.btn_refresh.setText("刷新声音")
            self.statusBar.showMessage("刷新声音ID数据时出错", 3000)
            # 显示错误
            self.append_log(f"刷新声音ID数据时出错: {str(e)}")
            QMessageBox.critical(
                self,
                "刷新声音ID失败",
                f"刷新声音ID数据时发生错误:\n{str(e)}"
            )
    
    def refresh_voice_id_data(self, force_update=False):
        """刷新声音ID数据"""
        try:
            # 获取配置参数
            use_online = self.config_manager.get("use_online_sheet", False)
            voice_id_excel = self.config_manager.get("voice_id_excel", config.VOICE_ID_EXCEL)

            # 检查在线模式的配置
            if use_online:
                baidu_sheet_url = self.config_manager.get("baidu_sheet_url", "")
                baidu_sheets_token = self.config_manager.get("baidu_sheets_token", "")

                if not baidu_sheet_url or not baidu_sheets_token:
                    QMessageBox.warning(
                        self,
                        "配置不完整",
                        "刷新失败，请在设置中输入知识库链接及API。\n\n请确保已正确配置：\n• 知识库URL\n• 百度表格API密钥"
                    )
                    self.append_log("错误: 在线模式需要配置知识库URL和API密钥")
                    return

            if not use_online and not os.path.exists(voice_id_excel):
                QMessageBox.warning(
                    self,
                    "文件不存在",
                    f"声音ID文件不存在：{voice_id_excel}\n\n请检查文件路径或切换到在线模式。"
                )
                self.append_log(f"错误: 声音ID文件 '{voice_id_excel}' 不存在")
                return
            
            # 显示进度对话框
            progress_dlg = QProgressDialog("正在刷新声音ID数据...", "取消", 0, 100, self)
            progress_dlg.setWindowTitle("刷新数据")
            progress_dlg.setWindowModality(Qt.WindowModal)
            progress_dlg.setMinimumDuration(0)
            progress_dlg.setValue(10)
            
            # 创建并启动刷新线程
            max_retries = 3  # 最大重试次数
            retry_count = 0
            success = False
            
            while retry_count < max_retries and not success:
                if retry_count > 0:
                    self.append_log(f"重试刷新 ({retry_count}/{max_retries})...")
                
                # 更新进度对话框
                progress_dlg.setValue(10 + retry_count * 20)
                QApplication.processEvents()
                
                try:
                    # 显示当前处理状态
                    def show_progress():
                        progress_dlg.setValue(50)
                        QApplication.processEvents()
                    
                    # 使用定时器更新进度
                    QTimer.singleShot(500, show_progress)
                    
                    # 使用处理器加载声音ID数据
                    if use_online:
                        # 从设置页面发起的刷新时使用force_refresh=True强制绕过缓存
                        voice_ids = self.processor.load_voice_ids(use_online=True, force_refresh=force_update)
                    else:
                        voice_ids = self.processor.load_voice_ids(voice_id_excel=voice_id_excel)
                    
                    # 检查是否成功加载
                    if voice_ids:
                        success = True
                        self.append_log(f"成功刷新声音ID数据，共 {len(voice_ids)} 个条目")

                        # 保存到本地Excel文件
                        try:
                            self.processor.update_local_voice_id_excel(voice_ids)
                            self.append_log("声音ID数据已保存到本地Excel文件")
                        except Exception as save_error:
                            self.append_log(f"保存声音ID到本地文件时出错: {str(save_error)}")

                        # 如果是从设置页面触发的刷新，显示更详细的日志
                        if force_update:
                            # 输出前10个声音ID的键值对
                            self.append_log("声音ID数据示例（前10个）:")
                            for i, (name, id) in enumerate(list(voice_ids.items())[:10]):
                                self.append_log(f"  {name}: {id}")

                            # 添加日志分隔线
                            self.append_log("----------------------")
                    else:
                        self.append_log("刷新声音ID数据失败，未获取到任何数据")
                except Exception as e:
                    self.append_log(f"刷新声音ID数据出错: {str(e)}")
                
                # 增加重试计数
                retry_count += 1
                
                # 检查是否取消
                if progress_dlg.wasCanceled():
                    break
            
            # 关闭进度对话框
            progress_dlg.setValue(100)
            progress_dlg.close()

            # 显示结果
            if success:
                voice_count = len(self.processor.voice_id_map) if hasattr(self.processor, 'voice_id_map') and self.processor.voice_id_map else 0
                self.statusBar.showMessage("声音ID数据刷新成功", 5000)
                data_source = "在线知识库" if use_online else "本地文件"
                QMessageBox.information(
                    self,
                    "刷新成功",
                    f"从{data_source}刷新成功，已更新 {voice_count} 个声音ID。"
                )
            else:
                self.statusBar.showMessage("声音ID数据刷新失败", 5000)
                data_source = "在线知识库" if use_online else "本地文件"
                QMessageBox.warning(
                    self,
                    "刷新失败",
                    f"从{data_source}刷新声音ID数据失败。\n\n请检查网络连接和配置设置。"
                )
                
        except Exception as e:
            self.append_log(f"刷新声音ID数据时出错: {str(e)}")
            self.statusBar.showMessage("声音ID数据刷新失败", 5000)
            QMessageBox.critical(
                self,
                "刷新错误",
                f"刷新声音ID数据时发生错误：\n\n{str(e)}\n\n请检查网络连接和配置设置。"
            )
    
    def refresh_script_data(self):
        """刷新文案脚本数据"""
        # 如果有当前加载的文件，就刷新该文件
        if hasattr(self.processor, 'current_loaded_file') and self.processor.current_loaded_file and os.path.exists(self.processor.current_loaded_file):
            excel_path = self.processor.current_loaded_file
            self.append_log(f"刷新当前加载的文件: {excel_path}")
        else:
            # 获取当前日期
            date_today = datetime.now().strftime("%Y%m%d")
            
            # 构建文件路径
            excel_path = os.path.join(f"生成结果_{date_today}", f"文案_{date_today}.xlsx")
            self.append_log(f"刷新今日文件: {excel_path}")
        
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            self.append_log(f"找不到文案文件: {excel_path}")
            return
        
        # 加载文件
        try:
            df = self.processor.load_scripts(excel_path)
            if df is not None and not df.empty:
                # 更新表格
                self.update_table_from_dataframe(df, append=False)
                
                # 记录当前加载的文件
                self.processor.current_loaded_file = excel_path
                self.current_loaded_file = excel_path
                
                # 显示消息
                self.statusBar.showMessage(f"已从 {excel_path} 刷新 {len(df)} 条文案", 3000)
                self.append_log(f"成功刷新 {len(df)} 条文案数据")
            else:
                self.append_log("刷新文案数据失败: 文件为空或格式不正确")
        except Exception as e:
            self.append_log(f"刷新文案数据时出错: {str(e)}")
            raise

    def on_editor_closed(self, editor, hint):
        """编辑器关闭后确保行高一致"""
        try:
            # 获取当前正在编辑的单元格
            current_index = self.table_scripts.currentIndex()
            if current_index.isValid():
                row = current_index.row()
                col = current_index.column()
                
                # 获取当前编辑的单元格文本
                item = self.table_scripts.item(row, col)
                if item:
                    cell_text = item.text()
                    
                    # 如果不是操作列且有文本内容
                    if col != 9 and cell_text:
                        # 使用更准确的方法计算文本所需高度
                        doc = QTextEdit()
                        doc.setHtml(cell_text)
                        doc.setLineWrapMode(QTextEdit.WidgetWidth)
                        doc.setWordWrapMode(QTextOption.WrapMode.WordWrap)
                        
                        # 使用当前列的宽度进行计算，而不是固定使用克隆文案列宽度
                        col_width = self.table_scripts.columnWidth(col)
                        doc.setFixedWidth(col_width - 10)  # 设置固定宽度，减去边距
                        doc.document().adjustSize()  # 调整文档大小
                        
                        # 计算适当的行高
                        doc_height = doc.document().size().height()
                        
                        # 增加额外的空间
                        padding = 40  # 增加边距
                        
                        # 计算行高，限制最小和最大高度
                        new_height = min(max(self.default_row_height, doc_height + padding), 300)
                        
                        # 设置行高
                        current_height = self.table_scripts.rowHeight(row)
                        if new_height > current_height:
                            self.table_scripts.setRowHeight(row, new_height)
                            print(f"编辑后调整行 {row} 高度为 {new_height}，列 {col}，文本长度: {len(cell_text)}, 文档高度: {doc_height}")
            
            # 为所有行设置最小行高
            for row in range(self.table_scripts.rowCount()):
                current_height = self.table_scripts.rowHeight(row)
                if current_height < self.default_row_height:
                    self.table_scripts.setRowHeight(row, self.default_row_height)
        except Exception as e:
            print(f"行高调整错误: {e}")

    def ensure_uniform_row_height(self):
        """确保表格行高一致"""
        # 注释掉这个方法的内容或直接返回，不执行任何调整
        return
        
        # 原有代码...

    @Slot(str)
    def on_search_text_changed(self, text):
        """搜索文本变化时自动搜索"""
        if len(text) >= 2:  # 至少输入2个字符才开始搜索
            # 清除之前的搜索结果
            self.clear_search_highlights()
            
            # 开始新的搜索
            self.search_table()
            
    @Slot()
    def on_search_clicked(self):
        """搜索按钮点击处理"""
        # 清除之前的搜索结果
        self.clear_search_highlights()
        
        # 开始新的搜索
        self.search_table()
    
    def search_table(self, next=False, prev=False):
        """在表格中搜索内容
        
        Args:
            next: 如果为True，则查找下一个匹配项
            prev: 如果为True，则查找上一个匹配项
            如果next和prev都为False，则重新搜索
        """
        # 获取搜索文本
        search_text = self.search_box.text().strip().lower()
        if not search_text:
            return
            
        # 如果不是查找下一个或上一个，初始化搜索变量
        if not next and not prev:
            self.search_results = []
            self.current_search_index = -1
            self.statusBar.showMessage("正在搜索...", 1000)
            
            # 查找所有匹配项
            for row in range(self.table_scripts.rowCount()):
                for col in range(self.table_scripts.columnCount() - 1):  # 不搜索操作列
                    item = self.table_scripts.item(row, col)
                    if item and search_text in item.text().lower():
                        self.search_results.append((row, col))
                        
            # 显示搜索结果数量
            if self.search_results:
                self.statusBar.showMessage(f"找到 {len(self.search_results)} 个匹配项", 3000)
            else:
                self.statusBar.showMessage("未找到匹配内容", 3000)
                return
        
        # 没有找到结果
        if not self.search_results:
            return
            
        # 计算下一个或上一个结果的索引
        if next:
            self.current_search_index = (self.current_search_index + 1) % len(self.search_results)
        elif prev:
            self.current_search_index = (self.current_search_index - 1) if self.current_search_index > 0 else len(self.search_results) - 1
        else:
            self.current_search_index = 0
            
        # 获取当前匹配项的位置
        row, col = self.search_results[self.current_search_index]
        
        # 滚动到该位置并选中单元格
        self.table_scripts.setCurrentCell(row, col)
        
        # 确保该行可见
        self.table_scripts.scrollToItem(self.table_scripts.item(row, col))
        
        # 添加高亮效果
        self.highlight_search_result(row, col)
        
        # 更新状态栏
        self.statusBar.showMessage(f"当前显示第 {self.current_search_index + 1}/{len(self.search_results)} 个匹配项", 3000)
    
    def clear_search_highlights(self):
        """清除所有搜索高亮"""
        # 如果有存储的搜索结果，恢复其原始背景色
        if hasattr(self, 'search_results') and self.search_results:
            for row, col in self.search_results:
                item = self.table_scripts.item(row, col)
                if item:
                    # 对于状态列，保留其状态颜色
                    if col == 7:  # 状态列
                        status = item.text()
                        self.update_status_cell_color(row, status)
                    else:
                        # 恢复默认背景色
                        item.setBackground(QColor(255, 255, 255) if not self.dark_mode else QColor(52, 58, 64))
    
    def highlight_search_result(self, row, col):
        """高亮显示搜索结果"""
        item = self.table_scripts.item(row, col)
        if item:
            # 设置高亮背景色
            item.setBackground(QColor(255, 255, 0, 100))  # 半透明黄色

    def on_search_text_changed(self):
        """搜索文本改变时自动搜索"""
        search_text = self.search_box.text().strip()
        if search_text:
            # 清除之前的搜索结果
            self.clear_search_highlights()
            # 开始新的搜索
            self.search_table()
        else:
            # 清空搜索，清除高亮
            self.clear_search_highlights()

    def clear_search(self):
        """清除搜索内容"""
        self.search_box.clear()
        # 清除高亮
        self.clear_search_highlights()

    def update_processor_data_from_table(self):
        """从表格更新处理器数据"""
        # 获取表格数据
        rows = []
        for row in range(self.table_scripts.rowCount()):
            # 获取单元格值
            row_data = {}
            for col in range(9):  # 获取前9列的数据
                col_name = self.table_scripts.horizontalHeaderItem(col).text()
                cell_value = self.table_scripts.item(row, col).text() if self.table_scripts.item(row, col) else ""
                row_data[col_name] = cell_value
            
            # 检查是否是空行 - 如果所有值都为空
            if all(val.strip() == "" for val in row_data.values()):
                continue
                
            rows.append(row_data)
        
        # 创建DataFrame
        df = pd.DataFrame(rows)
        
        # 更新处理器数据
        self.processor.scripts_df = df

        # 减少调试输出，只在数据变化时输出
        if hasattr(self, '_last_processor_data_count'):
            if self._last_processor_data_count != len(df):
                print(f"更新处理器数据: {len(df)}行")
                self._last_processor_data_count = len(df)
        else:
            print(f"更新处理器数据: {len(df)}行")
            self._last_processor_data_count = len(df)
        return df

    def toggle_baidu_sheet_url(self, state):
        """根据复选框状态启用或禁用百度表格URL输入框和相关按钮"""
        # 直接使用state的布尔值，而不是与Qt.Checked比较
        # 在PySide6中，复选框状态为0（未选中）或2（选中）
        enabled = bool(state)
        self.txt_baidu_sheet_url.setEnabled(enabled)
        self.btn_open_baidu_sheet.setEnabled(enabled)  # 同时启用/禁用打开按钮
        print(f"百度表格URL输入框状态更新: enabled={enabled}, state={state}")  # 添加调试信息
    
    def toggle_baidu_api_settings(self, state):
        """根据百度表格复选框状态启用或禁用API设置"""
        # 直接使用state的布尔值，而不是与Qt.Checked比较
        enabled = bool(state)
        self.chk_use_api.setEnabled(enabled)
        
        # 确保当百度表格未选中时，API复选框始终处于未选中状态
        if not enabled:
            self.chk_use_api.setChecked(False)
            self.txt_baidu_sheets_token.setEnabled(False)
        else:
            # 如果百度表格已启用，根据API复选框状态启用或禁用令牌输入框
            self.toggle_baidu_api_token(self.chk_use_api.isChecked())
        print(f"百度表格API设置状态更新: enabled={enabled}, state={state}")  # 添加调试信息
    
    def toggle_baidu_api_token(self, state):
        """根据是否使用API复选框状态启用或禁用API令牌输入框"""
        # 直接使用state的布尔值，而不是与Qt.Checked比较
        # 只有在百度表格已启用且API复选框已勾选的情况下才启用令牌输入框
        enabled = bool(state) and self.chk_use_online_sheet.isChecked()
        self.txt_baidu_sheets_token.setEnabled(enabled)
        print(f"API令牌输入框状态更新: enabled={enabled}, state={state}")  # 添加调试信息
    
    def refresh_baidu_sheet_data(self):
        """手动刷新声音ID数据"""
        if not self.chk_use_online_sheet.isChecked():
            QMessageBox.warning(
                self,
                "刷新声音ID数据",
                "百度在线表格功能未启用，请先启用后再刷新。"
            )
            return
            
        # 获取百度表格URL
        baidu_sheet_url = self.txt_baidu_sheet_url.text().strip()
        if not baidu_sheet_url:
            QMessageBox.warning(
                self,
                "刷新声音ID数据",
                "请先输入百度表格URL。"
            )
            return
            
        # 如果使用API，检查访问令牌
        if self.chk_use_api.isChecked():
            api_token = self.txt_baidu_sheets_token.text().strip()
            if not api_token:
                QMessageBox.warning(
                    self,
                    "刷新声音ID数据",
                    "已启用API模式，请输入API访问令牌。"
                )
                return
        
        # 保存当前设置到配置管理器
        self.config_manager.set("use_online_sheet", self.chk_use_online_sheet.isChecked())
        self.config_manager.set("baidu_sheet_url", baidu_sheet_url)
        self.config_manager.set("use_baidu_api", self.chk_use_api.isChecked())
        self.config_manager.set("baidu_sheets_token", self.txt_baidu_sheets_token.text())
        
        # 显示加载提示
        self.statusBar.showMessage("正在刷新声音ID数据...", 2000)
        
        # 在后台线程中刷新数据
        try:
            # 使用异步方法但同步调用
            loop = asyncio.get_event_loop()
            if loop.is_running():
                QMessageBox.warning(
                    self,
                    "刷新声音ID数据",
                    "事件循环已在运行，无法刷新声音ID数据。请稍后再试。"
                )
                return
                
            # 根据当前设置选择刷新方法
            try:
                use_api = self.chk_use_api.isChecked()
                if use_api:
                    self.append_log("使用百度表格API刷新声音ID数据（强制模式）...")
                    # 添加force_refresh=True参数强制刷新
                    voice_id_map = loop.run_until_complete(
                        self.processor.load_voice_ids_from_baidu_sheet_api(baidu_sheet_url, force_refresh=True)
                    )
                else:
                    self.append_log("使用网页抓取方式刷新声音ID数据...")
                    voice_id_map = loop.run_until_complete(
                        self.processor.load_voice_ids_from_baidu_sheet(baidu_sheet_url)
                    )
                    
                if voice_id_map:
                    self.append_log(f"成功刷新了 {len(voice_id_map)} 个声音ID")

                    # 保存到本地Excel文件
                    try:
                        self.processor.update_local_voice_id_excel(voice_id_map)
                        self.append_log("声音ID数据已保存到本地Excel文件")
                    except Exception as save_error:
                        self.append_log(f"保存声音ID到本地Excel文件时出错: {str(save_error)}")

                    # 缓存数据到本地文件
                    try:
                        cache_file = os.path.join("config", "baidu_sheet_cache.json")
                        os.makedirs(os.path.dirname(cache_file), exist_ok=True)

                        cache_data = {
                            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "voice_ids": voice_id_map
                        }

                        with open(cache_file, "w", encoding="utf-8") as f:
                            json.dump(cache_data, f, ensure_ascii=False, indent=2)

                        self.append_log(f"声音ID数据已缓存到本地文件: {cache_file}")
                    except Exception as e:
                        self.append_log(f"缓存声音ID数据到本地文件时出错: {e}")
                    
                    QMessageBox.information(
                        self,
                        "刷新成功",
                        f"已成功从百度表格获取并刷新了 {len(voice_id_map)} 个声音ID。"
                    )
                else:
                    QMessageBox.warning(
                        self,
                        "刷新失败",
                        "从百度表格获取数据失败，未能获取到有效的声音ID。"
                    )
            except Exception as e:
                self.append_log(f"刷新百度表格数据时出错: {e}")
                QMessageBox.critical(
                    self,
                    "刷新失败",
                    f"刷新百度表格数据时出错: {str(e)}"
                )
        except Exception as e:
            self.append_log(f"初始化事件循环时出错: {e}")
            QMessageBox.critical(
                self,
                "刷新失败",
                f"初始化事件循环时出错: {str(e)}"
            )

    @Slot()
    def open_baidu_sheet_in_browser(self):
        """在浏览器中打开知识库"""
        baidu_sheet_url = self.txt_baidu_sheet_url.text().strip()
        if baidu_sheet_url:
            try:
                import webbrowser
                webbrowser.open(baidu_sheet_url)
                self.append_log(f"已打开知识库: {baidu_sheet_url}")
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "打开知识库失败",
                    f"无法打开知识库: {str(e)}"
                )
        else:
            QMessageBox.warning(
                self,
                "知识库URL未设置",
                "请先输入知识库URL。"
            )

    def create_log_area(self):
        """创建折叠式日志区域"""
        # 创建日志容器
        self.log_container = QFrame()
        self.log_container.setObjectName("contentCard")  # 使用内容卡片样式
        log_layout = QVBoxLayout(self.log_container)
        log_layout.setContentsMargins(0, 0, 0, 0)
        log_layout.setSpacing(0)

        # 创建折叠按钮（标题栏）
        self.log_toggle_button = QPushButton()
        self.log_toggle_button.setObjectName("logToggleButton")
        self.log_toggle_button.setFixedHeight(48)  # 设置固定高度
        self.log_toggle_button.clicked.connect(self.toggle_log_panel)

        # 创建按钮内容布局
        button_layout = QHBoxLayout(self.log_toggle_button)
        button_layout.setContentsMargins(16, 0, 16, 0)
        button_layout.setSpacing(12)

        # 添加标题文字（移除图标）
        self.log_title_label = QLabel("处理日志")
        self.log_title_label.setObjectName("logTitle")
        button_layout.addWidget(self.log_title_label)

        # 添加日志计数
        self.log_count_label = QLabel("(0)")
        self.log_count_label.setObjectName("logCount")
        button_layout.addWidget(self.log_count_label)

        # 添加弹性空间
        button_layout.addStretch()

        # 添加折叠图标
        self.log_chevron = QLabel()
        arrow_down_icon = self.get_icon("arrow_down.svg")
        if not arrow_down_icon.isNull():
            self.log_chevron.setPixmap(arrow_down_icon.pixmap(20, 20))
        else:
            self.log_chevron.setText("▼")
        button_layout.addWidget(self.log_chevron)

        # 创建日志内容区域
        self.log_content_widget = QWidget()
        self.log_content_widget.setObjectName("logContent")
        content_layout = QVBoxLayout(self.log_content_widget)
        content_layout.setContentsMargins(16, 8, 16, 16)  # 增加8px上边距，避免遮挡分割线
        content_layout.setSpacing(0)

        # 创建日志文本框
        self.txt_log = QTextEdit()
        self.txt_log.setReadOnly(True)
        self.txt_log.setLineWrapMode(QTextEdit.WidgetWidth)
        self.txt_log.setMaximumHeight(200)  # 增加最大高度
        self.txt_log.setObjectName("logTextArea")
        content_layout.addWidget(self.txt_log)

        # 添加组件到主布局
        log_layout.addWidget(self.log_toggle_button)
        log_layout.addWidget(self.log_content_widget)

        # 初始状态为折叠
        self.log_expanded = False
        self.log_content_widget.setVisible(False)
        self.log_count = 0

    @Slot()
    def toggle_log_panel(self):
        """切换日志面板的展开/折叠状态"""
        self.log_expanded = not self.log_expanded
        self.log_content_widget.setVisible(self.log_expanded)

        # 更新折叠图标
        if self.log_expanded:
            arrow_up_icon = self.get_icon("arrow_up.svg")
            if not arrow_up_icon.isNull():
                self.log_chevron.setPixmap(arrow_up_icon.pixmap(20, 20))
            else:
                self.log_chevron.setText("▲")
        else:
            arrow_down_icon = self.get_icon("arrow_down.svg")
            if not arrow_down_icon.isNull():
                self.log_chevron.setPixmap(arrow_down_icon.pixmap(20, 20))
            else:
                self.log_chevron.setText("▼")

    def update_log_count(self):
        """更新日志计数显示"""
        self.log_count_label.setText(f"({self.log_count})")

    @Slot()
    def toggle_convert_numbers(self):
        """切换数字转汉字选项的可用状态"""
        # 根据文案自动转换的状态来设置数字转汉字选项的可用状态
        self.chk_convert_numbers.setEnabled(self.chk_text_conversion.isChecked())
        if not self.chk_text_conversion.isChecked():
            # 如果文案自动转换被禁用，则同时禁用数字转汉字选项
            self.chk_convert_numbers.setChecked(False)

    # 声音管理功能方法
    @Slot()
    def on_audio_location_clicked(self):
        """音频位置按钮点击处理"""
        if not self.voice_manager:
            QMessageBox.warning(self, "操作失败", "声音管理器未初始化，请重启程序。")
            return

        self.voice_manager.open_audio_folder()

    @Slot()
    def on_fetch_ids_clicked(self):
        """获取ID按钮点击处理"""
        if not self.voice_manager:
            QMessageBox.warning(self, "操作失败", "声音管理器未初始化，请重启程序。")
            return

        # 获取API密钥（使用与声音克隆相同的配置键）
        api_key = self.config_manager.get("api_key", "")
        if not api_key:
            QMessageBox.warning(
                self,
                "配置错误",
                "请先在设置中配置Fish Audio API密钥"
            )
            return

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认获取",
            "确定要从Fish Audio获取声音模型ID吗？\n这可能需要一些时间。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            # 禁用按钮防止重复点击
            self.btn_fetch_ids.setEnabled(False)
            self.btn_fetch_ids.setText("获取中...")

            # 在新线程中执行获取操作
            import threading
            thread = threading.Thread(
                target=self.voice_manager.fetch_voice_models,
                args=(api_key,)
            )
            thread.daemon = True
            thread.start()

    @Slot(str)
    def on_vm_search_text_changed(self, text):
        """声音管理搜索文本变化时自动搜索"""
        if len(text) >= 2:  # 至少输入2个字符才开始搜索
            self.vm_search_table()
        elif len(text) == 0:  # 清空搜索框时显示所有行
            self.clear_vm_search()

    @Slot()
    def on_vm_search_clicked(self):
        """声音管理搜索按钮点击处理"""
        self.vm_search_table()

    def vm_search_table(self, next=False, prev=False):
        """在声音管理表格中搜索内容

        Args:
            next: 如果为True，则查找下一个匹配项
            prev: 如果为True，则查找上一个匹配项
            如果next和prev都为False，则重新搜索
        """
        search_text = self.vm_search_box.text().strip().lower()
        if not search_text:
            self.clear_vm_search()
            return

        # 如果不是查找下一个或上一个，初始化搜索变量
        if not next and not prev:
            self.vm_search_results = []
            self.vm_current_search_index = -1

            # 清除之前的高亮
            self.clear_vm_search_highlights()

            # 查找所有匹配项
            for row in range(self.vm_table.rowCount()):
                for col in range(self.vm_table.columnCount()):
                    item = self.vm_table.item(row, col)
                    if item and search_text in item.text().lower():
                        self.vm_search_results.append((row, col))

            # 显示搜索结果数量
            if self.vm_search_results:
                self.append_vm_log(f"搜索 '{search_text}' 找到 {len(self.vm_search_results)} 个匹配项")
            else:
                self.append_vm_log(f"搜索 '{search_text}' 未找到匹配内容")
                return

        # 没有找到结果
        if not hasattr(self, 'vm_search_results') or not self.vm_search_results:
            return

        # 处理导航
        if next:
            self.vm_current_search_index = (self.vm_current_search_index + 1) % len(self.vm_search_results)
        elif prev:
            self.vm_current_search_index = (self.vm_current_search_index - 1) % len(self.vm_search_results)
        else:
            self.vm_current_search_index = 0

        # 高亮当前匹配项
        self.highlight_vm_search_result()

    def highlight_vm_search_result(self):
        """高亮当前搜索结果"""
        if not hasattr(self, 'vm_search_results') or not self.vm_search_results:
            return

        # 清除之前的高亮
        self.clear_vm_search_highlights()

        # 获取当前匹配项
        row, col = self.vm_search_results[self.vm_current_search_index]

        # 高亮当前项
        item = self.vm_table.item(row, col)
        if item:
            item.setBackground(QColor("#ffeb3b"))  # 黄色高亮

        # 滚动到当前项
        self.vm_table.scrollToItem(item)

        # 更新状态信息
        current_pos = self.vm_current_search_index + 1
        total_count = len(self.vm_search_results)
        self.append_vm_log(f"搜索结果 {current_pos}/{total_count}")

    def clear_vm_search_highlights(self):
        """清除声音管理搜索高亮"""
        for row in range(self.vm_table.rowCount()):
            for col in range(self.vm_table.columnCount()):
                item = self.vm_table.item(row, col)
                if item:
                    item.setBackground(QColor())  # 清除背景色

    def clear_vm_search(self):
        """清除声音管理搜索"""
        self.vm_search_box.clear()
        # 清除高亮
        self.clear_vm_search_highlights()
        # 清除搜索结果
        if hasattr(self, 'vm_search_results'):
            self.vm_search_results = []
        self.append_vm_log("已清除搜索过滤")

    @Slot()
    def on_start_voice_upload_clicked(self):
        """开始上传按钮点击处理"""
        if not self.voice_manager:
            QMessageBox.warning(self, "操作失败", "声音管理器未初始化，请重启程序。")
            return

        # 获取API密钥（复用声音克隆的API密钥）
        api_key = self.config_manager.get("api_key", "")
        if not api_key:
            QMessageBox.warning(
                self,
                "配置错误",
                "请先在设置中配置Fish Audio API密钥"
            )
            return

        # 检查音频文件夹中是否有文件
        audio_files = self.voice_manager.scan_audio_files()
        if not audio_files:
            QMessageBox.information(
                self,
                "提示",
                f"未发现待上传的音频文件\n\n请将音频文件放入以下文件夹：\n{self.voice_manager.AUDIO_UPLOAD_DIR}"
            )
            return

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认上传",
            f"发现 {len(audio_files)} 个音频文件，确定要上传到Fish Audio进行声音克隆吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            # 禁用按钮防止重复点击
            self.btn_start_voice_upload.setEnabled(False)
            self.btn_start_voice_upload.setText("上传中...")

            # 显示进度条
            self.vm_progress_bar.setVisible(True)
            self.vm_progress_bar.setValue(0)
            self.vm_progress_bar.setMaximum(len(audio_files))
            self.vm_progress_bar.setFormat("准备上传...")

            # 在新线程中执行上传操作
            import threading
            thread = threading.Thread(
                target=self.voice_manager.upload_audio_files,
                args=(api_key, audio_files)
            )
            thread.daemon = True
            thread.start()

    @Slot()
    def load_vm_table_data(self):
        """加载声音管理表格数据"""
        if not hasattr(self, 'vm_table') or not self.voice_manager:
            return

        try:
            voice_models = self.voice_manager.get_voice_models()

            # 清空表格
            self.vm_table.setRowCount(0)

            # 填充数据
            for i, model in enumerate(voice_models):
                self.vm_table.insertRow(i)

                # 序号
                self.vm_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))

                # 名称
                self.vm_table.setItem(i, 1, QTableWidgetItem(model.get('name', '')))

                # 模型ID
                self.vm_table.setItem(i, 2, QTableWidgetItem(model.get('modelId', '')))

                # 网址
                self.vm_table.setItem(i, 3, QTableWidgetItem(model.get('url', '')))

                # 提取时间
                self.vm_table.setItem(i, 4, QTableWidgetItem(model.get('extractTime', '')))

            self.append_vm_log(f"已加载 {len(voice_models)} 条声音模型数据")

        except Exception as e:
            self.append_vm_log(f"加载表格数据失败: {str(e)}")

    @Slot(int)
    def update_vm_progress(self, progress):
        """更新声音管理进度"""
        if hasattr(self, 'vm_progress_bar') and self.vm_progress_bar.isVisible():
            self.vm_progress_bar.setValue(progress)
            current = self.vm_progress_bar.value()
            maximum = self.vm_progress_bar.maximum()
            self.vm_progress_bar.setFormat(f"上传进度: {current}/{maximum}")

    @Slot(int)
    def on_vm_fetch_completed(self, new_count):
        """声音模型获取/上传完成处理"""
        # 重新启用获取ID按钮
        if hasattr(self, 'btn_fetch_ids'):
            self.btn_fetch_ids.setEnabled(True)
            self.btn_fetch_ids.setText("获取ID")

        # 重新启用上传按钮
        if hasattr(self, 'btn_start_voice_upload'):
            self.btn_start_voice_upload.setEnabled(True)
            self.btn_start_voice_upload.setText("开始上传")

        # 隐藏进度条
        if hasattr(self, 'vm_progress_bar'):
            self.vm_progress_bar.setVisible(False)
            self.vm_progress_bar.setValue(0)

        # 显示结果
        if new_count > 0:
            # 判断是获取还是上传操作
            if hasattr(self, 'btn_start_voice_upload') and not self.btn_start_voice_upload.isEnabled():
                # 上传操作
                QMessageBox.information(
                    self,
                    "上传完成",
                    f"成功上传 {new_count} 个音频文件并创建声音模型！"
                )
            else:
                # 获取操作
                QMessageBox.information(
                    self,
                    "获取完成",
                    f"成功获取 {new_count} 个新的声音模型！"
                )
        else:
            QMessageBox.information(
                self,
                "操作完成",
                "未发现新的声音模型。"
            )
    
