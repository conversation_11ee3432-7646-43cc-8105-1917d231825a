#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试字体修复
验证搜索高亮中的字体设置是否已修复，避免Qt字体警告
"""

import os
import sys
import re

def test_font_fix():
    """测试字体修复"""
    print("=== 测试搜索高亮字体修复 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查问题字体设置是否已移除
        problem_patterns = [
            (r'QFont\("", -1, QFont\.Bold\)', "空字体名称+无效大小", False),
            (r'QFont\("MS Sans Serif"', "MS Sans Serif字体", False),
            (r'QFont\(.*-1.*\)', "无效字体大小", False),
        ]
        
        # 检查正确的字体设置是否存在
        correct_patterns = [
            (r'font = .*\.font\(\)', "获取当前字体", True),
            (r'font\.setBold\(True\)', "设置粗体", True),
            (r'setFont\(font\)', "应用字体", True),
            (r'避免字体警告', "字体警告注释", True),
        ]
        
        print("🔍 检查问题字体设置:")
        problems_found = False
        for pattern, description, should_exist in problem_patterns:
            matches = re.findall(pattern, content)
            count = len(matches)
            
            if should_exist:
                success = count > 0
                status = f"✅ 找到 {count} 处" if success else "❌ 未找到"
            else:
                success = count == 0
                status = f"✅ 已移除" if success else f"❌ 仍有 {count} 处"
            
            print(f"  {description}: {status}")
            if not success and not should_exist:
                problems_found = True
        
        print("\n🔍 检查正确字体设置:")
        correct_found = True
        for pattern, description, should_exist in correct_patterns:
            matches = re.findall(pattern, content)
            count = len(matches)
            
            if should_exist:
                success = count > 0
                status = f"✅ 找到 {count} 处" if success else "❌ 未找到"
            else:
                success = count == 0
                status = f"✅ 已移除" if success else f"❌ 仍有 {count} 处"
            
            print(f"  {description}: {status}")
            if not success:
                correct_found = False
        
        # 检查具体的字体设置代码
        print(f"\n📍 字体设置代码检查:")
        
        # 查找字体相关的代码块
        font_sections = re.findall(r'# 使用系统默认字体.*?setFont\(font\)', content, re.DOTALL)
        
        for i, section in enumerate(font_sections):
            print(f"  字体设置 {i+1}:")
            lines = section.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line:
                    print(f"    {line}")
        
        return not problems_found and correct_found
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def explain_font_issue():
    """解释字体问题"""
    print("\n=== 字体警告问题说明 ===")
    
    print("🔍 原始问题:")
    print("  qt.qpa.fonts: DirectWrite: CreateFontFaceFromHDC() failed")
    print("  for QFontDef(Family=\"MS Sans Serif\", ...)")
    
    print(f"\n❌ 问题原因:")
    print(f"  原始代码: QFont(\"\", -1, QFont.Bold)")
    print(f"  - 空字体名称 \"\" 导致Qt使用系统默认字体")
    print(f"  - 无效字体大小 -1 可能导致字体创建失败")
    print(f"  - Qt回退到 \"MS Sans Serif\" 但创建失败")
    
    print(f"\n✅ 修复方案:")
    print(f"  新代码:")
    print(f"    font = highlighted_item.font()  # 获取当前字体")
    print(f"    font.setBold(True)              # 设置粗体")
    print(f"    highlighted_item.setFont(font)  # 应用字体")
    
    print(f"\n💡 修复优势:")
    print(f"  - 使用已有的有效字体，避免字体创建失败")
    print(f"  - 只修改粗体属性，保持其他字体属性")
    print(f"  - 兼容不同操作系统的字体设置")
    print(f"  - 消除终端中的字体警告信息")

def test_search_functionality():
    """测试搜索功能是否受影响"""
    print("\n=== 测试搜索功能完整性 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查搜索相关功能是否完整
        search_features = [
            (r'def.*search.*vm', "视频管理搜索函数", True),
            (r'def.*search.*voice', "音频管理搜索函数", True),
            (r'setBackground.*highlight_color', "背景色设置", True),
            (r'setForeground.*text_color', "前景色设置", True),
            (r'setBold\(True\)', "粗体设置", True),
            (r'selectRow\(row\)', "行选择", True),
            (r'setCurrentCell', "单元格选择", True),
        ]
        
        print("🔍 搜索功能完整性检查:")
        all_features_ok = True
        
        for pattern, description, should_exist in search_features:
            matches = re.findall(pattern, content)
            count = len(matches)
            
            if should_exist:
                success = count > 0
                status = f"✅ 找到 {count} 处" if success else "❌ 未找到"
            else:
                success = count == 0
                status = f"✅ 已移除" if success else f"❌ 仍有 {count} 处"
            
            print(f"  {description}: {status}")
            if not success:
                all_features_ok = False
        
        return all_features_ok
        
    except Exception as e:
        print(f"❌ 搜索功能测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 字体修复功能测试")
    print("=" * 60)
    
    # 测试字体修复
    font_ok = test_font_fix()
    
    # 解释字体问题
    explain_font_issue()
    
    # 测试搜索功能完整性
    search_ok = test_search_functionality()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    print(f"🎯 测试结果:")
    print(f"  - 字体修复: {'✅ 通过' if font_ok else '❌ 失败'}")
    print(f"  - 搜索功能: {'✅ 完整' if search_ok else '❌ 受影响'}")
    
    overall_success = font_ok and search_ok
    
    if overall_success:
        print(f"\n🎉 字体修复成功！")
        print(f"\n📋 修复总结:")
        print(f"  ✅ 移除了有问题的字体设置")
        print(f"  ✅ 使用安全的字体设置方法")
        print(f"  ✅ 保持了搜索高亮的粗体效果")
        print(f"  ✅ 消除了终端字体警告")
        
        print(f"\n🚀 用户体验改进:")
        print(f"  - 终端不再显示字体警告信息")
        print(f"  - 搜索高亮功能正常工作")
        print(f"  - 粗体效果依然存在")
        print(f"  - 兼容性更好")
        
        print(f"\n💡 技术改进:")
        print(f"  - 使用现有字体而不是创建新字体")
        print(f"  - 避免了字体创建失败的风险")
        print(f"  - 减少了系统资源消耗")
        print(f"  - 提高了代码的健壮性")
    else:
        print(f"\n❌ 部分修复可能不完整，需要进一步检查")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
