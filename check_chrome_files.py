#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查Chrome文件状态
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_chrome_files():
    """检查Chrome文件状态"""
    try:
        from core.video_material_manager import VideoMaterialManager
        from core.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建视频素材管理器
        manager = VideoMaterialManager(config_manager)
        
        print("=== Chrome文件状态检查 ===")
        chrome_dir = manager.chrome_debug_dir
        print(f"Chrome目录: {chrome_dir}")
        
        # 检查Default目录
        default_dir = os.path.join(chrome_dir, "Default")
        print(f"Default目录存在: {os.path.exists(default_dir)}")
        
        if os.path.exists(default_dir):
            print("\n=== Default目录内容 ===")
            try:
                files = os.listdir(default_dir)
                cookie_related = [f for f in files if 'cookie' in f.lower()]
                login_related = [f for f in files if 'login' in f.lower() or 'web data' in f.lower()]
                
                print(f"总文件数: {len(files)}")
                print(f"Cookie相关文件: {cookie_related}")
                print(f"登录相关文件: {login_related}")
                
                # 检查具体的Cookie文件
                cookie_files = [
                    "Cookies",
                    "Cookies-journal", 
                    "Network\\Cookies",
                    "cookies.sqlite",
                    "Cookies.bak"
                ]
                
                print("\n=== Cookie文件检查 ===")
                for cookie_file in cookie_files:
                    cookie_path = os.path.join(default_dir, cookie_file)
                    if os.path.exists(cookie_path):
                        file_size = os.path.getsize(cookie_path)
                        print(f"✅ {cookie_file}: {file_size} 字节")
                    else:
                        print(f"❌ {cookie_file}: 不存在")
                
                # 查找所有包含cookie的文件
                print("\n=== 搜索所有Cookie相关文件 ===")
                for file in files:
                    if 'cookie' in file.lower():
                        file_path = os.path.join(default_dir, file)
                        if os.path.isfile(file_path):
                            file_size = os.path.getsize(file_path)
                            print(f"🔍 找到: {file} ({file_size} 字节)")
                
            except Exception as e:
                print(f"读取目录失败: {str(e)}")
        
        # 检查Chrome进程状态
        print("\n=== Chrome进程状态 ===")
        has_chrome = manager.check_existing_chrome_processes()
        print(f"检测到Chrome进程: {'是' if has_chrome else '否'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_chrome_files()
    if success:
        print("\n✅ 检查完成")
    else:
        print("\n❌ 检查失败")
        sys.exit(1)
